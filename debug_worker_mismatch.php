<?php
/**
 * Debug why workers page shows 3 workers but attendance page shows 0
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'admin/config/config.php';

echo "<h1>Debug Worker Mismatch Issue</h1>\n";

$today = date('Y-m-d');

try {
    // Step 1: Check direct database query
    echo "<h2>Step 1: Direct Database Query</h2>\n";
    
    $pdo = getDbConnection();
    
    // Check all workers
    $stmt = $pdo->query("SELECT id, employee_id, name, department, status, deleted_at FROM workers ORDER BY id");
    $all_workers = $stmt->fetchAll();
    
    echo "<p><strong>Total workers in database:</strong> " . count($all_workers) . "</p>\n";
    
    if (count($all_workers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>Employee ID</th><th>Name</th><th>Department</th><th>Status</th><th>Deleted</th></tr>\n";
        foreach ($all_workers as $worker) {
            $deleted = $worker['deleted_at'] ? 'YES' : 'NO';
            $color = ($worker['status'] === 'active' && !$worker['deleted_at']) ? 'green' : 'red';
            echo "<tr style='color: $color;'>";
            echo "<td>{$worker['id']}</td>";
            echo "<td>{$worker['employee_id']}</td>";
            echo "<td>{$worker['name']}</td>";
            echo "<td>{$worker['department']}</td>";
            echo "<td>{$worker['status']}</td>";
            echo "<td>$deleted</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Check active workers specifically (what attendance API should return)
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM workers WHERE deleted_at IS NULL AND status = 'active'");
    $active_count = $stmt->fetch()['count'];
    echo "<p><strong>Active workers (deleted_at IS NULL AND status = 'active'):</strong> $active_count</p>\n";
    
    // Step 2: Test Workers API (what workers page uses)
    echo "<h2>Step 2: Test Workers API</h2>\n";
    
    $workers_response = makeApiRequest('/attendance/workers.php');
    echo "<p><strong>Workers API Success:</strong> " . ($workers_response['success'] ? 'Yes' : 'No') . "</p>\n";
    echo "<p><strong>Workers API HTTP Code:</strong> {$workers_response['http_code']}</p>\n";
    
    if ($workers_response['success'] && isset($workers_response['data'])) {
        $workers_data = $workers_response['data'];
        if (isset($workers_data['workers'])) {
            $api_workers = $workers_data['workers'];
            echo "<p><strong>Workers from Workers API:</strong> " . count($api_workers) . "</p>\n";
            
            if (count($api_workers) > 0) {
                echo "<h3>Workers from Workers API:</h3>\n";
                echo "<ul>\n";
                foreach ($api_workers as $worker) {
                    echo "<li>ID: {$worker['id']}, Name: {$worker['name']}, Status: {$worker['status']}</li>\n";
                }
                echo "</ul>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ No 'workers' key in Workers API response</p>\n";
            echo "<p><strong>Available keys:</strong> " . implode(', ', array_keys($workers_data)) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Workers API failed</p>\n";
        echo "<pre>" . htmlspecialchars(print_r($workers_response, true)) . "</pre>\n";
    }
    
    // Step 3: Test Attendance API (what attendance page uses)
    echo "<h2>Step 3: Test Attendance API</h2>\n";
    
    $attendance_response = makeApiRequest("/attendance/attendance.php?action=date&date=$today");
    echo "<p><strong>Attendance API Success:</strong> " . ($attendance_response['success'] ? 'Yes' : 'No') . "</p>\n";
    echo "<p><strong>Attendance API HTTP Code:</strong> {$attendance_response['http_code']}</p>\n";
    
    if ($attendance_response['success'] && isset($attendance_response['data'])) {
        $attendance_data = $attendance_response['data'];
        if (isset($attendance_data['workers'])) {
            $attendance_workers = $attendance_data['workers'];
            echo "<p><strong>Workers from Attendance API:</strong> " . count($attendance_workers) . "</p>\n";
            
            if (count($attendance_workers) > 0) {
                echo "<h3>Workers from Attendance API:</h3>\n";
                echo "<ul>\n";
                foreach ($attendance_workers as $worker) {
                    echo "<li>ID: {$worker['id']}, Name: {$worker['name']}, Status: " . ($worker['status'] ?? 'N/A') . "</li>\n";
                }
                echo "</ul>\n";
            } else {
                echo "<p style='color: red;'>❌ Attendance API returned empty workers array</p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ No 'workers' key in Attendance API response</p>\n";
            echo "<p><strong>Available keys:</strong> " . implode(', ', array_keys($attendance_data)) . "</p>\n";
        }
        
        // Show full response for debugging
        echo "<h3>Full Attendance API Response:</h3>\n";
        echo "<pre>" . htmlspecialchars(print_r($attendance_response, true)) . "</pre>\n";
    } else {
        echo "<p style='color: red;'>❌ Attendance API failed</p>\n";
        echo "<pre>" . htmlspecialchars(print_r($attendance_response, true)) . "</pre>\n";
    }
    
    // Step 4: Test the exact query from getDateAttendance function
    echo "<h2>Step 4: Test Exact Query from getDateAttendance</h2>\n";
    
    try {
        $stmt = $pdo->prepare("
            SELECT id, employee_id, name, department, position, 'not_marked' as status
            FROM workers 
            WHERE deleted_at IS NULL AND status = 'active'
            ORDER BY name ASC
        ");
        $stmt->execute();
        $direct_workers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Direct query result:</strong> " . count($direct_workers) . " workers</p>\n";
        
        if (count($direct_workers) > 0) {
            echo "<h3>Workers from Direct Query:</h3>\n";
            echo "<ul>\n";
            foreach ($direct_workers as $worker) {
                echo "<li>ID: {$worker['id']}, Employee ID: {$worker['employee_id']}, Name: {$worker['name']}, Department: {$worker['department']}</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p style='color: red;'>❌ Direct query returned no workers</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Direct query failed: " . $e->getMessage() . "</p>\n";
    }
    
    // Step 5: Compare the differences
    echo "<h2>Step 5: Analysis</h2>\n";
    
    if ($active_count > 0 && count($attendance_workers ?? []) == 0) {
        echo "<p style='color: red;'>🔍 <strong>ISSUE FOUND:</strong> Database has $active_count active workers, but Attendance API returns 0 workers</p>\n";
        echo "<p>This suggests there's an issue in the getDateAttendance() function or API routing.</p>\n";
    } elseif ($active_count == 0) {
        echo "<p style='color: red;'>🔍 <strong>ISSUE FOUND:</strong> No active workers in database</p>\n";
        echo "<p>Workers might have wrong status or deleted_at values.</p>\n";
    } else {
        echo "<p style='color: green;'>✅ Worker counts match between database and API</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<p><a href='admin/attendance.php' style='color: blue;'>Go back to Attendance Page</a></p>\n";
echo "<p><a href='admin/workers.php' style='color: blue;'>Go to Workers Page</a></p>\n";
?>
