<?php
/**
 * Attendance API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/attendance.php' from segments
$segments = array_slice($segments, 3);

// Handle query parameters as fallback for clean URLs
$action = $_GET['action'] ?? null;
$date = $_GET['date'] ?? null;
$worker_id = $_GET['worker_id'] ?? null;

// If we have query parameters, use them to simulate clean URL segments
if ($action && empty($segments[0])) {
    switch ($action) {
        case 'date':
            if ($date) {
                $segments = ['date', $date];
            }
            break;
        case 'worker':
            if ($worker_id) {
                $segments = ['worker', $worker_id];
            }
            break;
        case 'bulk':
            $segments = ['bulk'];
            break;
    }
}

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Validate required fields
 */
function validateRequired($data, $fields) {
    $missing = [];
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// Handle the request based on method and endpoint
try {
    switch ($method) {
        case 'GET':
            // Handle different GET endpoints
            if (isset($segments[0])) {
                switch ($segments[0]) {
                    case 'worker':
                        // Get attendance for specific worker
                        if (isset($segments[1]) && is_numeric($segments[1])) {
                            getWorkerAttendance($segments[1]);
                        } else {
                            sendJsonResponse(400, null, 'Worker ID is required');
                        }
                        break;
                    case 'date':
                        // Get attendance for specific date
                        if (isset($segments[1])) {
                            getDateAttendance($segments[1]);
                        } else {
                            sendJsonResponse(400, null, 'Date is required');
                        }
                        break;
                    case 'report':
                        // Get attendance report
                        getAttendanceReport();
                        break;
                    default:
                        if (is_numeric($segments[0])) {
                            getAttendance($segments[0]);
                        } else {
                            getAllAttendance();
                        }
                }
            } else {
                getAllAttendance();
            }
            break;
            
        case 'POST':
            // Handle different POST endpoints
            if (isset($segments[0]) && $segments[0] === 'bulk') {
                createBulkAttendance();
            } else {
                createAttendance();
            }
            break;
            
        case 'PUT':
        case 'PATCH':
            // Update existing attendance
            if (isset($segments[0]) && is_numeric($segments[0])) {
                updateAttendance($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Attendance ID is required for update');
            }
            break;
            
        case 'DELETE':
            // Delete attendance record
            if (isset($segments[0]) && is_numeric($segments[0])) {
                deleteAttendance($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Attendance ID is required for deletion');
            }
            break;
            
        default:
            sendJsonResponse(405, null, 'Method not allowed');
    }
} catch (Exception $e) {
    error_log("Attendance API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get all attendance records with filtering
 */
function getAllAttendance() {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        // Build query with filters
        $where = ['1=1'];
        $params = [];
        
        // Filter by date range
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $where[] = 'wa.attendance_date >= ?';
            $params[] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $where[] = 'wa.attendance_date <= ?';
            $params[] = $_GET['end_date'];
        }
        
        // Filter by worker
        if (isset($_GET['worker_id']) && !empty($_GET['worker_id'])) {
            $where[] = 'wa.worker_id = ?';
            $params[] = $_GET['worker_id'];
        }
        
        // Filter by status
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $where[] = 'wa.status = ?';
            $params[] = $_GET['status'];
        }
        
        // Filter by department
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $where[] = 'w.department = ?';
            $params[] = $_GET['department'];
        }
        
        // Pagination
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 20;
        $offset = ($page - 1) * $limit;
        
        // Count total records
        $countSql = "SELECT COUNT(*) as total 
                     FROM worker_attendance wa 
                     JOIN workers w ON wa.worker_id = w.id 
                     WHERE " . implode(' AND ', $where);
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];
        
        // Get attendance records
        $sql = "SELECT wa.id, wa.worker_id, wa.attendance_date, wa.status, 
                       wa.check_in_time, wa.check_out_time, wa.break_duration, 
                       wa.overtime_hours, wa.notes, wa.created_at, wa.updated_at,
                       w.employee_id, w.name as worker_name, w.department, w.position
                FROM worker_attendance wa 
                JOIN workers w ON wa.worker_id = w.id 
                WHERE " . implode(' AND ', $where) . "
                ORDER BY wa.attendance_date DESC, w.name ASC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $attendance = $stmt->fetchAll();
        
        sendJsonResponse(200, [
            'attendance' => $attendance,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Get all attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve attendance records');
    }
}

/**
 * Get single attendance record by ID
 */
function getAttendance($id) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        $stmt = $pdo->prepare("
            SELECT wa.id, wa.worker_id, wa.attendance_date, wa.status, 
                   wa.check_in_time, wa.check_out_time, wa.break_duration, 
                   wa.overtime_hours, wa.notes, wa.created_at, wa.updated_at,
                   w.employee_id, w.name as worker_name, w.department, w.position
            FROM worker_attendance wa 
            JOIN workers w ON wa.worker_id = w.id 
            WHERE wa.id = ?
        ");
        $stmt->execute([$id]);
        $attendance = $stmt->fetch();
        
        if (!$attendance) {
            sendJsonResponse(404, null, 'Attendance record not found');
            return;
        }
        
        sendJsonResponse(200, $attendance);
        
    } catch (Exception $e) {
        error_log("Get attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve attendance record');
    }
}

/**
 * Get attendance for specific worker
 */
function getWorkerAttendance($workerId) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        // Check if worker exists
        $stmt = $pdo->prepare("SELECT id, name FROM workers WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$workerId]);
        $worker = $stmt->fetch();
        
        if (!$worker) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }
        
        // Get date range (default to current month)
        $startDate = $_GET['start_date'] ?? date('Y-m-01');
        $endDate = $_GET['end_date'] ?? date('Y-m-t');
        
        // Get attendance records
        $stmt = $pdo->prepare("
            SELECT id, attendance_date, status, check_in_time, check_out_time, 
                   break_duration, overtime_hours, notes, created_at, updated_at
            FROM worker_attendance 
            WHERE worker_id = ? AND attendance_date BETWEEN ? AND ?
            ORDER BY attendance_date DESC
        ");
        $stmt->execute([$workerId, $startDate, $endDate]);
        $attendance = $stmt->fetchAll();
        
        // Calculate statistics
        $totalDays = count($attendance);
        $presentDays = count(array_filter($attendance, function($a) { return $a['status'] === 'present'; }));
        $absentDays = count(array_filter($attendance, function($a) { return $a['status'] === 'absent'; }));
        $lateDays = count(array_filter($attendance, function($a) { return $a['status'] === 'late'; }));
        $halfDays = count(array_filter($attendance, function($a) { return $a['status'] === 'half_day'; }));
        
        $attendancePercentage = $totalDays > 0 ? round(($presentDays + $lateDays + $halfDays) / $totalDays * 100, 2) : 0;
        
        sendJsonResponse(200, [
            'worker' => $worker,
            'attendance' => $attendance,
            'statistics' => [
                'total_days' => $totalDays,
                'present_days' => $presentDays,
                'absent_days' => $absentDays,
                'late_days' => $lateDays,
                'half_days' => $halfDays,
                'attendance_percentage' => $attendancePercentage
            ],
            'date_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Get worker attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve worker attendance');
    }
}

/**
 * Get attendance for specific date
 */
function getDateAttendance($date) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Validate date format
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            sendJsonResponse(400, null, 'Invalid date format. Use YYYY-MM-DD');
            return;
        }

        // Get all active workers
        $stmt = $pdo->prepare("
            SELECT id, employee_id, name, department, position, 'not_marked' as status
            FROM workers 
            WHERE deleted_at IS NULL AND status = 'active'
            ORDER BY name ASC
        ");
        $stmt->execute();
        $workers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get attendance records for the date
        $attendanceStmt = $pdo->prepare("
            SELECT wa.id as attendance_id, wa.worker_id, wa.status, wa.check_in_time,
                   wa.check_out_time, wa.break_duration, wa.overtime_hours, wa.notes
            FROM worker_attendance wa
            WHERE wa.attendance_date = ?
        ");
        $attendanceStmt->execute([$date]);
        $attendanceRecords = [];
        while ($row = $attendanceStmt->fetch(PDO::FETCH_ASSOC)) {
            $attendanceRecords[$row['worker_id']] = $row;
        }

        // Merge worker data with attendance records
        foreach ($workers as &$worker) {
            $workerId = $worker['id'];
            if (isset($attendanceRecords[$workerId])) {
                $worker = array_merge($worker, $attendanceRecords[$workerId]);
            }
        }

        // Calculate summary statistics
        $totalWorkers = count($workers);
        $presentCount = 0;
        $absentCount = 0;
        $lateCount = 0;
        $halfDayCount = 0;
        $notMarkedCount = 0;

        foreach ($workers as $worker) {
            if (isset($worker['attendance_id']) && $worker['attendance_id']) {
                switch ($worker['status']) {
                    case 'present':
                        $presentCount++;
                        break;
                    case 'absent':
                        $absentCount++;
                        break;
                    case 'late':
                        $lateCount++;
                        break;
                    case 'half_day':
                        $halfDayCount++;
                        break;
                }
            } else {
                $notMarkedCount++;
            }
        }

        // Format the response to match what the frontend expects
        $response = [
            'date' => $date,
            'workers' => $workers,
            'summary' => [
                'total_workers' => $totalWorkers,
                'present' => $presentCount,
                'absent' => $absentCount,
                'late' => $lateCount,
                'half_day' => $halfDayCount,
                'not_marked' => $notMarkedCount
            ]
        ];

        sendJsonResponse(200, $response);

    } catch (Exception $e) {
        error_log("Get date attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve date attendance');
    }
}

/**
 * Create attendance record
 */
function createAttendance() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided');
            return;
        }

        // Validate required fields
        $required = ['worker_id', 'attendance_date', 'status'];
        $missing = validateRequired($data, $required);
        if (!empty($missing)) {
            sendJsonResponse(400, null, 'Missing required fields: ' . implode(', ', $missing));
            return;
        }

        // Sanitize input
        $data = sanitizeInput($data);

        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Check if worker exists
        $stmt = $pdo->prepare("SELECT id FROM workers WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$data['worker_id']]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }

        // Check if attendance already exists for this date
        $stmt = $pdo->prepare("SELECT id FROM worker_attendance WHERE worker_id = ? AND attendance_date = ?");
        $stmt->execute([$data['worker_id'], $data['attendance_date']]);
        if ($stmt->fetch()) {
            sendJsonResponse(409, null, 'Attendance already exists for this date');
            return;
        }

        // Insert new attendance record
        $stmt = $pdo->prepare("
            INSERT INTO worker_attendance (worker_id, attendance_date, status, check_in_time,
                                         check_out_time, break_duration, overtime_hours, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $data['worker_id'],
            $data['attendance_date'],
            $data['status'],
            $data['check_in_time'] ?? null,
            $data['check_out_time'] ?? null,
            $data['break_duration'] ?? 0,
            $data['overtime_hours'] ?? 0.00,
            $data['notes'] ?? null,
            1 // TODO: Get from authenticated user
        ]);

        $attendanceId = $pdo->lastInsertId();

        // Get the created attendance record
        $stmt = $pdo->prepare("
            SELECT wa.id, wa.worker_id, wa.attendance_date, wa.status,
                   wa.check_in_time, wa.check_out_time, wa.break_duration,
                   wa.overtime_hours, wa.notes, wa.created_at, wa.updated_at,
                   w.employee_id, w.name as worker_name, w.department, w.position
            FROM worker_attendance wa
            JOIN workers w ON wa.worker_id = w.id
            WHERE wa.id = ?
        ");
        $stmt->execute([$attendanceId]);
        $attendance = $stmt->fetch();

        sendJsonResponse(201, $attendance, 'Attendance record created successfully');

    } catch (Exception $e) {
        error_log("Create attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to create attendance record');
    }
}

/**
 * Create bulk attendance records
 */
function createBulkAttendance() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data) || !isset($data['attendance_date']) || !isset($data['workers'])) {
            sendJsonResponse(400, null, 'Invalid data format. Expected: {attendance_date, workers: [{worker_id, status, ...}]}');
            return;
        }

        $attendanceDate = $data['attendance_date'];
        $workers = $data['workers'];

        if (empty($workers) || !is_array($workers)) {
            sendJsonResponse(400, null, 'Workers array is required');
            return;
        }

        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        $pdo->beginTransaction();

        $created = [];
        $errors = [];

        foreach ($workers as $index => $workerData) {
            try {
                if (!isset($workerData['worker_id']) || !isset($workerData['status'])) {
                    $errors[] = "Worker at index $index: missing worker_id or status";
                    continue;
                }

                // Check if worker exists
                $stmt = $pdo->prepare("SELECT id FROM workers WHERE id = ? AND deleted_at IS NULL");
                $stmt->execute([$workerData['worker_id']]);
                if (!$stmt->fetch()) {
                    $errors[] = "Worker at index $index: worker not found";
                    continue;
                }

                // Check if attendance already exists
                $stmt = $pdo->prepare("SELECT id FROM worker_attendance WHERE worker_id = ? AND attendance_date = ?");
                $stmt->execute([$workerData['worker_id'], $attendanceDate]);
                if ($stmt->fetch()) {
                    // Update existing record
                    $stmt = $pdo->prepare("
                        UPDATE worker_attendance
                        SET status = ?, check_in_time = ?, check_out_time = ?,
                            break_duration = ?, overtime_hours = ?, notes = ?
                        WHERE worker_id = ? AND attendance_date = ?
                    ");
                    $stmt->execute([
                        $workerData['status'],
                        $workerData['check_in_time'] ?? null,
                        $workerData['check_out_time'] ?? null,
                        $workerData['break_duration'] ?? 0,
                        $workerData['overtime_hours'] ?? 0.00,
                        $workerData['notes'] ?? null,
                        $workerData['worker_id'],
                        $attendanceDate
                    ]);
                    $created[] = "Updated worker ID: " . $workerData['worker_id'];
                } else {
                    // Create new record
                    $stmt = $pdo->prepare("
                        INSERT INTO worker_attendance (worker_id, attendance_date, status, check_in_time,
                                                     check_out_time, break_duration, overtime_hours, notes, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $workerData['worker_id'],
                        $attendanceDate,
                        $workerData['status'],
                        $workerData['check_in_time'] ?? null,
                        $workerData['check_out_time'] ?? null,
                        $workerData['break_duration'] ?? 0,
                        $workerData['overtime_hours'] ?? 0.00,
                        $workerData['notes'] ?? null,
                        1 // TODO: Get from authenticated user
                    ]);
                    $created[] = "Created worker ID: " . $workerData['worker_id'];
                }

            } catch (Exception $e) {
                $errors[] = "Worker at index $index: " . $e->getMessage();
            }
        }

        $pdo->commit();

        sendJsonResponse(200, [
            'created' => $created,
            'errors' => $errors,
            'total_processed' => count($workers),
            'successful' => count($created),
            'failed' => count($errors)
        ], 'Bulk attendance processing completed');

    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        error_log("Create bulk attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to create bulk attendance records');
    }
}

/**
 * Update attendance record
 */
function updateAttendance($id) {
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided for update');
            return;
        }

        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Check if attendance record exists
        $stmt = $pdo->prepare("SELECT id FROM worker_attendance WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Attendance record not found');
            return;
        }

        // Sanitize input
        $data = sanitizeInput($data);

        // Prepare the SQL query
        $updates = [];
        $values = [];

        $allowedFields = [
            'status', 'check_in_time', 'check_out_time', 'break_duration',
            'overtime_hours', 'notes'
        ];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updates[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($updates)) {
            sendJsonResponse(400, null, 'No valid fields provided for update');
            return;
        }

        $values[] = $id;

        $sql = "UPDATE worker_attendance SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        // Get the updated attendance record
        $stmt = $pdo->prepare("
            SELECT wa.id, wa.worker_id, wa.attendance_date, wa.status,
                   wa.check_in_time, wa.check_out_time, wa.break_duration,
                   wa.overtime_hours, wa.notes, wa.created_at, wa.updated_at,
                   w.employee_id, w.name as worker_name, w.department, w.position
            FROM worker_attendance wa
            JOIN workers w ON wa.worker_id = w.id
            WHERE wa.id = ?
        ");
        $stmt->execute([$id]);
        $attendance = $stmt->fetch();

        sendJsonResponse(200, $attendance, 'Attendance record updated successfully');

    } catch (Exception $e) {
        error_log("Update attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to update attendance record');
    }
}

/**
 * Delete attendance record
 */
function deleteAttendance($id) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Check if attendance record exists
        $stmt = $pdo->prepare("SELECT id FROM worker_attendance WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Attendance record not found');
            return;
        }

        // Delete the record
        $stmt = $pdo->prepare("DELETE FROM worker_attendance WHERE id = ?");
        $stmt->execute([$id]);

        sendJsonResponse(200, null, 'Attendance record deleted successfully');

    } catch (Exception $e) {
        error_log("Delete attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to delete attendance record');
    }
}

/**
 * Get attendance report with statistics
 */
function getAttendanceReport() {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Get date range (default to current month)
        $startDate = $_GET['start_date'] ?? date('Y-m-01');
        $endDate = $_GET['end_date'] ?? date('Y-m-t');
        $department = $_GET['department'] ?? null;

        // Build where clause
        $where = ['wa.attendance_date BETWEEN ? AND ?'];
        $params = [$startDate, $endDate];

        if ($department) {
            $where[] = 'w.department = ?';
            $params[] = $department;
        }

        // Get detailed attendance data
        $sql = "SELECT w.id as worker_id, w.employee_id, w.name, w.department, w.position,
                       COUNT(wa.id) as total_days,
                       COUNT(CASE WHEN wa.status = 'present' THEN 1 END) as present_days,
                       COUNT(CASE WHEN wa.status = 'absent' THEN 1 END) as absent_days,
                       COUNT(CASE WHEN wa.status = 'late' THEN 1 END) as late_days,
                       COUNT(CASE WHEN wa.status = 'half_day' THEN 1 END) as half_days,
                       SUM(wa.overtime_hours) as total_overtime,
                       ROUND((COUNT(CASE WHEN wa.status IN ('present', 'late', 'half_day') THEN 1 END) / COUNT(wa.id)) * 100, 2) as attendance_percentage
                FROM workers w
                LEFT JOIN worker_attendance wa ON w.id = wa.worker_id AND " . implode(' AND ', $where) . "
                WHERE w.deleted_at IS NULL AND w.status = 'active'
                GROUP BY w.id, w.employee_id, w.name, w.department, w.position
                ORDER BY w.name ASC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $workerStats = $stmt->fetchAll();

        // Get overall statistics
        $overallSql = "SELECT
                         COUNT(DISTINCT w.id) as total_workers,
                         COUNT(wa.id) as total_records,
                         COUNT(CASE WHEN wa.status = 'present' THEN 1 END) as total_present,
                         COUNT(CASE WHEN wa.status = 'absent' THEN 1 END) as total_absent,
                         COUNT(CASE WHEN wa.status = 'late' THEN 1 END) as total_late,
                         COUNT(CASE WHEN wa.status = 'half_day' THEN 1 END) as total_half_days,
                         SUM(wa.overtime_hours) as total_overtime_hours,
                         ROUND(AVG(CASE WHEN wa.status IN ('present', 'late', 'half_day') THEN 100 ELSE 0 END), 2) as overall_attendance_percentage
                       FROM workers w
                       LEFT JOIN worker_attendance wa ON w.id = wa.worker_id AND " . implode(' AND ', $where) . "
                       WHERE w.deleted_at IS NULL AND w.status = 'active'";

        $stmt = $pdo->prepare($overallSql);
        $stmt->execute($params);
        $overallStats = $stmt->fetch();

        // Get department-wise statistics
        $deptSql = "SELECT w.department,
                           COUNT(DISTINCT w.id) as workers_count,
                           COUNT(wa.id) as total_records,
                           COUNT(CASE WHEN wa.status = 'present' THEN 1 END) as present_count,
                           COUNT(CASE WHEN wa.status = 'absent' THEN 1 END) as absent_count,
                           COUNT(CASE WHEN wa.status = 'late' THEN 1 END) as late_count,
                           COUNT(CASE WHEN wa.status = 'half_day' THEN 1 END) as half_day_count,
                           ROUND(AVG(CASE WHEN wa.status IN ('present', 'late', 'half_day') THEN 100 ELSE 0 END), 2) as dept_attendance_percentage
                    FROM workers w
                    LEFT JOIN worker_attendance wa ON w.id = wa.worker_id AND " . implode(' AND ', $where) . "
                    WHERE w.deleted_at IS NULL AND w.status = 'active'
                    GROUP BY w.department
                    ORDER BY w.department ASC";

        $stmt = $pdo->prepare($deptSql);
        $stmt->execute($params);
        $departmentStats = $stmt->fetchAll();

        sendJsonResponse(200, [
            'date_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'worker_statistics' => $workerStats,
            'overall_statistics' => $overallStats,
            'department_statistics' => $departmentStats
        ]);

    } catch (Exception $e) {
        error_log("Get attendance report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to generate attendance report');
    }
}
