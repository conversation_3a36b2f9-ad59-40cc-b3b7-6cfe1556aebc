<?php
require_once 'config/config.php';

// Require login
requireLogin();

header('Content-Type: text/plain');

try {
    $pdo = getDbConnection();
    
    // Check if documents table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'documents'");
    if ($stmt->rowCount() === 0) {
        die("Documents table does not exist\n");
    }
    
    // Get document count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM documents");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Total documents in database: " . $count . "\n\n";
    
    // Get recent documents
    $stmt = $pdo->query("SELECT id, file_name, file_path, file_size_kb, upload_date, uploaded_by FROM documents ORDER BY upload_date DESC LIMIT 5");
    $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Recent documents (up to 5 most recent):\n";
    echo str_repeat("-", 100) . "\n";
    foreach ($recent as $doc) {
        echo "ID: " . $doc['id'] . "\n";
        echo "File: " . $doc['file_name'] . "\n";
        echo "Path: " . $doc['file_path'] . "\n";
        echo "Size: " . $doc['file_size_kb'] . " KB\n";
        echo "Date: " . $doc['upload_date'] . "\n";
        echo "By: " . $doc['uploaded_by'] . "\n";
        echo str_repeat("-", 100) . "\n";
    }
    
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage() . "\n");
}
