<?php
/**
 * Worker Management
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require admin role
requireAdmin();

$current_page = 'workers';
$page_title = 'Worker Management';

// Handle worker actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = getDbConnection();
        
        if ($action === 'toggle_status' && isset($_POST['worker_id'])) {
            $worker_id = (int)$_POST['worker_id'];
            $new_status = $_POST['new_status'];
            
            $stmt = $pdo->prepare("UPDATE workers SET status = ? WHERE id = ?");
            $stmt->execute([$new_status, $worker_id]);
            
            showAlert('Worker status updated successfully', 'success');
        }
        
        if ($action === 'delete' && isset($_POST['worker_id'])) {
            $worker_id = (int)$_POST['worker_id'];
            
            // Check if worker has attendance records
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM worker_attendance WHERE worker_id = ?");
            $stmt->execute([$worker_id]);
            $attendanceCount = $stmt->fetch()['count'];
            
            if ($attendanceCount > 0) {
                // Soft delete
                $stmt = $pdo->prepare("UPDATE workers SET deleted_at = NOW(), status = 'terminated' WHERE id = ?");
                $stmt->execute([$worker_id]);
                showAlert('Worker archived successfully (has attendance records)', 'warning');
            } else {
                // Hard delete
                $stmt = $pdo->prepare("DELETE FROM workers WHERE id = ?");
                $stmt->execute([$worker_id]);
                showAlert('Worker deleted successfully', 'success');
            }
        }
        
    } catch (Exception $e) {
        showAlert($e->getMessage(), 'danger');
    }
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Get workers
try {
    $pdo = getDbConnection();
    
    // Build query with filters
    $where = ['deleted_at IS NULL'];
    $params = [];
    
    // Filter by status
    if (isset($_GET['status']) && !empty($_GET['status'])) {
        $where[] = 'status = ?';
        $params[] = $_GET['status'];
    }
    
    // Filter by department
    if (isset($_GET['department']) && !empty($_GET['department'])) {
        $where[] = 'department = ?';
        $params[] = $_GET['department'];
    }
    
    // Search by name or employee_id
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $where[] = '(name LIKE ? OR employee_id LIKE ?)';
        $searchTerm = '%' . $_GET['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $stmt = $pdo->prepare("
        SELECT id, employee_id, name, email, phone, department, position, 
               hire_date, status, created_at, updated_at
        FROM workers 
        WHERE " . implode(' AND ', $where) . "
        ORDER BY name ASC
    ");
    $stmt->execute($params);
    $workers = $stmt->fetchAll();
    
    // Get worker statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_workers,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_workers,
            COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_workers,
            COUNT(CASE WHEN status = 'terminated' THEN 1 END) as terminated_workers,
            COUNT(DISTINCT department) as departments
        FROM workers
        WHERE deleted_at IS NULL
    ");
    $worker_stats = $stmt->fetch();
    
    // Get departments for filter
    $stmt = $pdo->query("
        SELECT DISTINCT department 
        FROM workers 
        WHERE deleted_at IS NULL AND department IS NOT NULL 
        ORDER BY department ASC
    ");
    $departments = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    error_log("Workers page error: " . $e->getMessage());
    $workers = [];
    $worker_stats = ['total_workers' => 0, 'active_workers' => 0, 'inactive_workers' => 0, 'terminated_workers' => 0, 'departments' => 0];
    $departments = [];
}

include 'includes/header.php';
?>

<!-- Worker Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Worker Management</h1>
        <p class="text-muted">Manage workers and employee information</p>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWorkerModal">
            <i class="bi bi-person-plus me-2"></i>
            Add New Worker
        </button>
    </div>
</div>

<!-- Worker Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Workers</h6>
                        <h3 class="mb-0"><?= $worker_stats['total_workers'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Active Workers</h6>
                        <h3 class="mb-0"><?= $worker_stats['active_workers'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-person-check" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Inactive Workers</h6>
                        <h3 class="mb-0"><?= $worker_stats['inactive_workers'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-person-dash" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Departments</h6>
                        <h3 class="mb-0"><?= $worker_stats['departments'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-building" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Name or Employee ID" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" <?= ($_GET['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= ($_GET['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    <option value="terminated" <?= ($_GET['status'] ?? '') === 'terminated' ? 'selected' : '' ?>>Terminated</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">Department</label>
                <select class="form-select" id="department" name="department">
                    <option value="">All Departments</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?= htmlspecialchars($dept) ?>" 
                                <?= ($_GET['department'] ?? '') === $dept ? 'selected' : '' ?>>
                            <?= htmlspecialchars($dept) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>Filter
                </button>
                <a href="workers.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Workers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-table me-2"></i>
            Workers List
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($workers)): ?>
            <div class="text-center py-4">
                <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">No workers found</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWorkerModal">
                    <i class="bi bi-person-plus me-2"></i>
                    Add First Worker
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Employee ID</th>
                            <th>Worker</th>
                            <th>Department</th>
                            <th>Position</th>
                            <th>Hire Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($workers as $worker): ?>
                            <tr>
                                <td>
                                    <span class="badge bg-secondary"><?= htmlspecialchars($worker['employee_id']) ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <?= strtoupper(substr($worker['name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($worker['name']) ?></div>
                                            <?php if ($worker['email']): ?>
                                                <small class="text-muted"><?= htmlspecialchars($worker['email']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($worker['department']): ?>
                                        <span class="badge bg-info"><?= htmlspecialchars($worker['department']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= htmlspecialchars($worker['position'] ?? '-') ?></td>
                                <td>
                                    <?php if ($worker['hire_date']): ?>
                                        <small><?= formatDate($worker['hire_date'], 'M j, Y') ?></small>
                                    <?php else: ?>
                                        <small class="text-muted">-</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $worker['status'] === 'active' ? 'success' : ($worker['status'] === 'inactive' ? 'warning' : 'danger') ?>">
                                        <?= ucfirst($worker['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary"
                                                onclick="editWorker(<?= htmlspecialchars(json_encode($worker)) ?>)"
                                                title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info"
                                                onclick="viewAttendance(<?= $worker['id'] ?>)"
                                                title="View Attendance">
                                            <i class="bi bi-calendar-check"></i>
                                        </button>
                                        <?php if ($worker['status'] !== 'terminated'): ?>
                                            <button type="button" class="btn btn-outline-<?= $worker['status'] === 'active' ? 'warning' : 'success' ?>"
                                                    onclick="toggleWorkerStatus(<?= $worker['id'] ?>, '<?= $worker['status'] === 'active' ? 'inactive' : 'active' ?>')"
                                                    title="<?= $worker['status'] === 'active' ? 'Deactivate' : 'Activate' ?>">
                                                <i class="bi bi-<?= $worker['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteWorker(<?= $worker['id'] ?>, '<?= htmlspecialchars($worker['name']) ?>')"
                                                    title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Worker Modal -->
<div class="modal fade" id="addWorkerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Worker</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addWorkerForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employee_id" class="form-label">Employee ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employee_id" name="employee_id" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" name="department"
                                       list="departmentList" placeholder="Enter or select department">
                                <datalist id="departmentList">
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= htmlspecialchars($dept) ?>">
                                    <?php endforeach; ?>
                                </datalist>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="position" name="position">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hire_date" class="form-label">Hire Date</label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Worker</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Worker Modal -->
<div class="modal fade" id="editWorkerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Worker</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editWorkerForm">
                <input type="hidden" id="edit_worker_id" name="worker_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_employee_id" class="form-label">Employee ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_employee_id" name="employee_id" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="edit_email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="edit_phone" name="phone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="edit_department" name="department"
                                       list="departmentList" placeholder="Enter or select department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="edit_position" name="position">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_hire_date" class="form-label">Hire Date</label>
                                <input type="date" class="form-control" id="edit_hire_date" name="hire_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_status" class="form-label">Status</label>
                                <select class="form-select" id="edit_status" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="terminated">Terminated</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Worker</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Base URL from PHP
const BASE_URL = '<?php echo rtrim(dirname($_SERVER['SCRIPT_NAME']), '/admin'); ?>';

// Worker management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add worker form submission
    document.getElementById('addWorkerForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        fetch(`${BASE_URL}/api/attendance/workers.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('Worker created successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addWorkerModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(result.message || 'Failed to create worker', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while creating worker', 'error');
        });
    });

    // Edit worker form submission
    document.getElementById('editWorkerForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const workerId = formData.get('worker_id');
        const data = Object.fromEntries(formData);
        delete data.worker_id;

        fetch(`${BASE_URL}/api/attendance/workers.php/${workerId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('Worker updated successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('editWorkerModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(result.message || 'Failed to update worker', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while updating worker', 'error');
        });
    });
});

// Edit worker function
function editWorker(worker) {
    document.getElementById('edit_worker_id').value = worker.id;
    document.getElementById('edit_employee_id').value = worker.employee_id || '';
    document.getElementById('edit_name').value = worker.name || '';
    document.getElementById('edit_email').value = worker.email || '';
    document.getElementById('edit_phone').value = worker.phone || '';
    document.getElementById('edit_department').value = worker.department || '';
    document.getElementById('edit_position').value = worker.position || '';
    document.getElementById('edit_hire_date').value = worker.hire_date || '';
    document.getElementById('edit_status').value = worker.status || 'active';

    new bootstrap.Modal(document.getElementById('editWorkerModal')).show();
}

// Toggle worker status
function toggleWorkerStatus(workerId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus === 'active' ? 'activate' : 'deactivate'} this worker?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_status">
            <input type="hidden" name="worker_id" value="${workerId}">
            <input type="hidden" name="new_status" value="${newStatus}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Delete worker
function deleteWorker(workerId, workerName) {
    if (confirm(`Are you sure you want to delete worker "${workerName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="worker_id" value="${workerId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// View attendance
function viewAttendance(workerId) {
    window.location.href = `attendance.php?worker_id=${workerId}`;
}
</script>

<?php include 'includes/footer.php'; ?>
