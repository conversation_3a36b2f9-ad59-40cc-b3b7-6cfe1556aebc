<?php
/**
 * Debug why attendance page shows no workers
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'admin/config/config.php';

echo "<h1>Debug Attendance Workers Issue</h1>\n";

$today = date('Y-m-d');
echo "<p><strong>Testing date:</strong> $today</p>\n";

// Step 1: Test direct database query
echo "<h2>Step 1: Direct Database Query</h2>\n";

try {
    $pdo = getDbConnection();
    
    // Check workers in database
    $stmt = $pdo->query("SELECT id, employee_id, name, department, status, deleted_at FROM workers ORDER BY employee_id");
    $db_workers = $stmt->fetchAll();
    
    echo "<p><strong>Total workers in database:</strong> " . count($db_workers) . "</p>\n";
    
    if (count($db_workers) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>Employee ID</th><th>Name</th><th>Department</th><th>Status</th><th>Deleted At</th></tr>\n";
        foreach ($db_workers as $worker) {
            $deleted = $worker['deleted_at'] ? 'YES' : 'NO';
            $color = $worker['status'] === 'active' && !$worker['deleted_at'] ? 'green' : 'red';
            echo "<tr style='color: $color;'>";
            echo "<td>{$worker['id']}</td>";
            echo "<td>{$worker['employee_id']}</td>";
            echo "<td>{$worker['name']}</td>";
            echo "<td>{$worker['department']}</td>";
            echo "<td>{$worker['status']}</td>";
            echo "<td>$deleted</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Check active workers specifically
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM workers WHERE deleted_at IS NULL AND status = 'active'");
    $active_count = $stmt->fetch()['count'];
    echo "<p><strong>Active workers (not deleted):</strong> $active_count</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>\n";
}

// Step 2: Test API endpoint directly
echo "<h2>Step 2: Test API Endpoint Directly</h2>\n";

$api_url = "http://192.168.0.106/MtcInvoiceNewProject/api/attendance/attendance.php?action=date&date=$today";
echo "<p><strong>API URL:</strong> $api_url</p>\n";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = file_get_contents($api_url, false, $context);
$http_code = 200;

if (isset($http_response_header)) {
    foreach ($http_response_header as $header) {
        if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
            $http_code = intval($matches[1]);
            break;
        }
    }
}

echo "<p><strong>HTTP Code:</strong> $http_code</p>\n";
echo "<p><strong>Raw Response:</strong></p>\n";
echo "<pre>" . htmlspecialchars($response) . "</pre>\n";

$json_data = json_decode($response, true);
if ($json_data) {
    echo "<p style='color: green;'>✅ Valid JSON response</p>\n";
    
    if (isset($json_data['success']) && $json_data['success']) {
        echo "<p style='color: green;'>✅ API call successful</p>\n";
        
        if (isset($json_data['data']['workers'])) {
            echo "<p><strong>Workers in API response:</strong> " . count($json_data['data']['workers']) . "</p>\n";
        } else {
            echo "<p style='color: red;'>❌ No 'workers' key in API response data</p>\n";
            echo "<p><strong>Available keys:</strong> " . implode(', ', array_keys($json_data['data'] ?? [])) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ API call failed</p>\n";
        echo "<p><strong>Error message:</strong> " . ($json_data['message'] ?? 'No message') . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Invalid JSON response</p>\n";
}

// Step 3: Test makeApiRequest function
echo "<h2>Step 3: Test makeApiRequest Function</h2>\n";

$api_response = makeApiRequest("/attendance/attendance.php?action=date&date=$today");

echo "<p><strong>makeApiRequest Success:</strong> " . ($api_response['success'] ? 'Yes' : 'No') . "</p>\n";
echo "<p><strong>makeApiRequest HTTP Code:</strong> {$api_response['http_code']}</p>\n";
echo "<p><strong>makeApiRequest Response:</strong></p>\n";
echo "<pre>" . htmlspecialchars(print_r($api_response, true)) . "</pre>\n";

// Step 4: Simulate the exact logic from attendance.php
echo "<h2>Step 4: Simulate Attendance.php Logic</h2>\n";

$attendance_response = makeApiRequest("/attendance/attendance.php?action=date&date=$today");

echo "<p><strong>Response success:</strong> " . ($attendance_response['success'] ? 'Yes' : 'No') . "</p>\n";

// Handle the response format from makeApiRequest (exact same logic as attendance.php)
if ($attendance_response['success'] && isset($attendance_response['data']['data'])) {
    $api_data = $attendance_response['data']['data'];
    $workers = $api_data['workers'] ?? [];
    $summary = $api_data['summary'] ?? [];
    echo "<p style='color: green;'>✅ Using nested data format</p>\n";
} else {
    // Fallback: try to get data directly from the response
    $api_data = $attendance_response['data'] ?? [];
    $workers = $api_data['workers'] ?? [];
    $summary = $api_data['summary'] ?? [];
    echo "<p style='color: orange;'>⚠️ Using fallback data format</p>\n";
}

echo "<p><strong>Final workers count:</strong> " . count($workers) . "</p>\n";
echo "<p><strong>Final summary:</strong> " . print_r($summary, true) . "</p>\n";

if (count($workers) > 0) {
    echo "<p style='color: green;'>✅ Workers found! The issue might be in the frontend display.</p>\n";
    echo "<h3>Sample Workers:</h3>\n";
    echo "<ul>\n";
    foreach (array_slice($workers, 0, 3) as $worker) {
        echo "<li>ID: {$worker['id']}, Name: {$worker['name']}, Status: " . ($worker['status'] ?? 'not_marked') . "</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<p style='color: red;'>❌ No workers found in final result</p>\n";
}

echo "<h2>Recommendations</h2>\n";
if (count($workers) > 0) {
    echo "<p style='color: green;'>The API is working correctly. The issue might be in the frontend JavaScript or PHP display logic.</p>\n";
} else {
    echo "<p style='color: red;'>The API is not returning workers. Check the getDateAttendance function in the API.</p>\n";
}

echo "<p><a href='admin/attendance.php' style='color: blue;'>Go back to Attendance Page</a></p>\n";
?>
