<?php
require_once 'config/config.php';

// Require login
requireLogin();

try {
    $pdo = getDbConnection();
    
    // Check if documents table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'documents'");
    if ($stmt->rowCount() === 0) {
        die("Documents table does not exist");
    }
    
    // Get table structure
    $stmt = $pdo->query("DESCRIBE documents");
    echo "<h2>Documents Table Structure</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Get document count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM documents");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>Total documents in database: " . $count . "</p>";
    
    // Get recent documents
    $stmt = $pdo->query("SELECT * FROM documents ORDER BY upload_date DESC LIMIT 5");
    echo "<h3>Recent Documents</h3>";
    echo "<table border='1' cellpadding='5'>";
    if ($stmt->rowCount() > 0) {
        $first = true;
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if ($first) {
                echo "<tr>";
                foreach (array_keys($row) as $key) {
                    echo "<th>" . htmlspecialchars($key) . "</th>";
                }
                echo "</tr>";
                $first = false;
            }
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</td>";
            }
            echo "</tr>";
        }
    } else {
        echo "<tr><td>No documents found</td></tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}
