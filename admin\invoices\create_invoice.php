<?php
require_once __DIR__ . '/../../admin/config/config.php';
requireLogin();

$page_title = 'Create Invoice';
include_once __DIR__ . '/../includes/header.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editable Quotation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            width: 100%;
            height: 100vh;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

        .scrollable-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: 20px;
        }

        [contenteditable="true"] {
            padding: 1px;
            cursor: text;
        }
        [contenteditable="true"]:focus {
            outline: none;
            background-color: #f9f9f9;
        }
        
        .header { text-align: center; margin-bottom: 20px; }
        .header .arabic-name { font-size: 24px; font-weight: bold; }
        .header .english-name { font-size: 20px; font-weight: bold; margin-bottom: 10px; }
        .header p { font-size: 12px; margin: 2px 0; }
        .quotation-title { text-align: center; font-size: 28px; font-weight: bold; text-decoration: underline; margin: 30px 0; }
        
        .meta-info { 
            display: flex; 
            flex-wrap: wrap;
            justify-content: space-between; 
            font-size: 14px; 
            margin: 0 0 20px 0;
            padding: 0 10px;
            width: 100%;
            box-sizing: border-box;
            align-items: flex-start;
            gap: 10px;
        }
        .info-right {
            text-align: right;
            flex: 1 1 45%;
            padding: 0;
            margin: 0;
            min-width: 200px;
        }
        .info-left {
            flex: 1 1 50%;
            padding: 0;
            margin: 0;
            min-width: 200px;
        }
        .info-left > p:first-child,
        .info-right > p:first-child {
            margin-top: 0;
            line-height: 1.5;
        }
        .info-left, .info-right { 
            flex: 1; 
        }

        b { font-weight: bold; }
        
        .items-table { width: 100%; border-collapse: collapse; border: 1px solid #000; margin-bottom: 20px; }
        .items-table th, .items-table tfoot td { 
            border: 1px solid #000; 
            padding: 8px; 
            text-align: center; 
            font-weight: bold;
        }
        .items-table th { background-color: #f2f2f2; }
        .items-table tbody tr { border-bottom: 1px solid #000; }
        .items-table td { padding: 0; border-left: 1px solid #000; /* Default left border */ vertical-align: middle; text-align: center; }
        .items-table td:first-child { border-left: none; vertical-align: middle; text-align: center; }
        /* The right border will be handled by the left border of the next cell */
        
        .items-table td textarea {
            width: 100%;
            border: none;
            outline: none;
            padding: 8px;
            box-sizing: border-box;
            background-color: transparent;
            text-align: center;
            font-family: inherit;
            font-size: inherit;
            resize: none;
            overflow: hidden;
            vertical-align: middle;
            font-weight: normal;
        }
        .items-table td textarea.description-field {
            text-align: left;
        }

        .banking-details { 
            display: none; /* Hidden by default */
            margin: 40px auto 20px; /* Top margin and centered */
            transition: all 0.3s ease;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        .banking-details > div {
            display: inline-block;
            text-align: left;
            margin: 0 auto;
        }
        .banking-details-table { width: 100%; max-width: 450px; border-collapse: collapse; border: 1px solid #000; }
        .banking-details-table td { border: 1px solid #000; padding: 8px; }
        .banking-details-table td:first-child { font-weight: bold; background-color: #f2f2f2; width: 35%; text-align: left; }
        .banking-details-table td:last-child { text-align: left; }

        .subject {
            margin: 20px 0;
            display: block;
            width: 100%;
        }
        .subject p {
            margin: 0;
            display: inline-block;
            border-bottom: 2px solid #000; /* Thicker underline */
            padding-bottom: 2px;
            line-height: 1.5;
        }
        .subject span[contenteditable="true"] {
            min-width: 70%;
            display: inline;
            padding-bottom: 2px;
            border: none; /* Remove border from span */
        }
        .subject span[contenteditable="true"]:focus {
            outline: none;
            background-color: #f5f5f5; /* Light gray background on focus for better UX */
        }

        .signature-section { margin-top: 30px; min-height: 150px; }
        .signature-image-display { display: none; max-width: 350px; max-height: 150px; border-bottom: 2px solid #000; }

        .bottom-controls {
            flex-shrink: 0;
            width: 100%;
            padding: 10px;
            background-color: #4CAF50;
            box-sizing: border-box;
        }
        .control-row { display: flex; justify-content: center; margin-bottom: 5px; }
        .ctrl-btn { border: 2px solid white; color: white; background-color: transparent; font-size: 14px; font-weight: bold; cursor: pointer; margin: 0 3px; padding: 8px; min-width: 60px; text-align: center; border-radius: 5px; flex-grow: 1; }
        .ctrl-btn.plus { background-color: #28a745; }
        .ctrl-btn.minus { background-color: #dc3545; }
        .ctrl-btn.action { background-color: #007bff; }
        .ctrl-btn.blue { background-color: #17a2b8; }
        .ctrl-btn.orange { background-color: #fd7e14; }

        /* --- CSS for Mobile Responsiveness --- */
        .table-wrapper {
            overflow-x: auto; /* Enables horizontal scrolling for the table */
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        }

        @media (max-width: 768px) {
            body {
                font-size: 14px; /* Adjust base font size for mobile */
            }

            .scrollable-content {
                padding: 10px; /* Reduce padding on mobile */
            }
            
            .meta-info {
                flex-direction: row;
                flex-wrap: nowrap;
                gap: 10px;
                align-items: flex-start;
                padding: 0 5px;
            }
            
            .info-right {
                text-align: right !important;
                padding: 0 !important;
                flex: 1 1 40%;
                min-width: 150px;
            }
            
            .info-left {
                flex: 1 1 55%;
                min-width: 150px;
                padding: 0 !important;
            }

            .header .arabic-name { font-size: 20px; }
            .header .english-name { font-size: 18px; }
            .header p { font-size: 11px; }
            .quotation-title { font-size: 22px; margin: 20px 0; }

            /* Stack meta info blocks on top of each other */
            .meta-info {
                flex-direction: column;
                gap: 10px;
            }

            /* Reset the position of the right info block */
            .info-right {
                position: static;
                width: 100%;
            }

            .subject p {
                display: block; /* Allow subject line to wrap better */
            }
            .subject span[contenteditable="true"] {
                min-width: auto;
            }

            /* Make table cells a bit smaller and prevent text wrapping */
            .items-table th, .items-table td textarea {
                padding: 6px;
                font-size: 13px;
                white-space: nowrap; /* Keep table content on one line */
            }

            .items-table td textarea.description-field {
                white-space: normal; /* Allow the description field to wrap */
                min-width: 250px; /* Give description column a decent width */
            }

            /* Adjust banking details layout */
            .banking-details {
                padding: 0 10px;
            }

            /* Style the control buttons for smaller screens */
            .control-row {
                flex-wrap: wrap; /* Allow buttons to wrap onto the next line */
                gap: 5px;
            }
            .ctrl-btn {
                padding: 8px;
                font-size: 13px;
                flex-basis: 48%; /* Make buttons take up roughly half the width */
            }
        }
    </style>
</head>
<body>

<div class="container">
    <div class="scrollable-content" id="content-to-save">
        <div class="header">
            <div class="arabic-name" contenteditable="true">مشاريع نبراس الخوض للتجارة والمقاولات</div>
            <div class="english-name" contenteditable="true">NABRAS AL-KHOUDH TRAD. & CONT. PROJECTS</div>
            <p contenteditable="true">ص.ب: 32، الخوض، سلطنة عمان، هاتف: ******** س.ر: 1129470</p>
            <p contenteditable="true">P.O. BOX 32, 123Al - khoudh, sultanate of Oman, Tel:******** C.R. NO: 1129470</p>
            <p contenteditable="true">E-mail: <EMAIL></p>
        </div>
        <div class="quotation-title" contenteditable="true">QUOTATION</div>
        
        <div class="meta-info">
            <div class="info-left">
                 <p><b>TO:</b></p>
                <div contenteditable="true">
                    Sohar International<br>
                    P.O. Box 44<br>
                    Hai Al Mina, PC 114<br>
                    Sultanate Of Oman<br>
                    VAT No: OM110000378X
                </div>
            </div>
            <div class="info-right" style="text-align: left;">
                <p><b>Date:</b> <span contenteditable="true">31/07/2025</span></p>
                <p><b>QR No:</b> <span contenteditable="true">QR 31072025SIB</span></p>
                <p><b>Location:</b> <span contenteditable="true">AKO Branch</span></p>
                <p><b>VAT IN:</b> <span contenteditable="true">OM1100198193</span></p>
            </div>
        </div>
        
        <div class="subject">
            <p><b>SUBJECT: </b><span contenteditable="true">Replacement Of Security Back Doors at Burimi</span></p>
        </div>

        <div class="table-wrapper">
            <table class="items-table">
                <thead id="table-header">
                    <tr><th>S.N</th><th>Description</th><th>QTY</th><th>UOM</th><th>Amount</th></tr>
                </thead>
                <tbody id="items-tbody"></tbody>
                <tfoot>
                    <tr>
                        <td id="table-footer-cell" contenteditable="true">
                            Total amount RO 300 /- (Rial Omani Three Hundred rial-Only)
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <div class="total-amount" style="text-align: left; margin: 20px 0;">
            <p contenteditable="true" style="margin: 0; font-weight: bold; text-decoration: underline; min-width: 400px; display: inline-block;">Total amount RO 609/- (Rial Omani Six Hundred Nine rial only)</p>
        </div>
        
        <div class="banking-details">
            <div>
                <h3 style="text-align: center; margin-bottom: 10px;">Banking Account Details</h3>
                <table class="banking-details-table">
                    <tbody>
                        <tr><td>Bank Name</td><td contenteditable="true">Bank Muscat</td></tr>
                        <tr><td>Account No</td><td contenteditable="true">****************</td></tr>
                        <tr><td>Account name holder</td><td contenteditable="true">NABRAS AL KHOUDH TRAD & CON. PROJECT</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="signature-section">
            <img id="signature-image" class="signature-image-display" alt="User Signature">
        </div>
    </div> 

    <div class="bottom-controls">
        <div class="control-row">
            <button id="add-row-btn" class="ctrl-btn plus">+ Row</button>
            <button id="remove-row-btn" class="ctrl-btn minus">- Row</button>
            <button id="add-col-btn" class="ctrl-btn plus">+ Column</button>
            <button id="remove-col-btn" class="ctrl-btn minus">- Column</button>
        </div>
        <div class="control-row">
            <button id="add-total-row-btn" class="ctrl-btn plus" title="Add Total Row">+ Total</button>
            <button id="remove-total-row-btn" class="ctrl-btn minus" title="Remove Total Row">- Total</button>
            <button id="show-banking-btn" class="ctrl-btn">👁️ Show Banking</button>
            <button id="hide-banking-btn" class="ctrl-btn" style="display: none;">👁️ Hide Banking</button>
            <button id="signature-trigger-btn" class="ctrl-btn orange">✍️ Signature</button>
            <button id="save-pdf-btn" class="ctrl-btn action">💾 Save PDF</button>
        </div>
    </div>
    
    <input type="file" id="signature-input" accept="image/*" style="display: none;">
</div>

<script>
// Toggle Banking Section
    // Get elements for total row management
    const addTotalRowBtn = document.getElementById('add-total-row-btn');
    const removeTotalRowBtn = document.getElementById('remove-total-row-btn');
    const tableFooter = document.querySelector('tfoot');
    
    // Banking section elements
    const showBankingBtn = document.getElementById('show-banking-btn');
    const hideBankingBtn = document.getElementById('hide-banking-btn');
    const bankingDetails = document.querySelector('.banking-details');
    
    showBankingBtn.addEventListener('click', function() {
        bankingDetails.style.display = 'block';
        showBankingBtn.style.display = 'none';
        hideBankingBtn.style.display = 'inline-block';
    });
    
    hideBankingBtn.addEventListener('click', function() {
        bankingDetails.style.display = 'none';
        hideBankingBtn.style.display = 'none';
        showBankingBtn.style.display = 'inline-block';
    });

    // Initialize with banking section hidden
    bankingDetails.style.display = 'none';
    showBankingBtn.style.display = 'inline-block';

    document.addEventListener('DOMContentLoaded', function () {
    const itemsTbody = document.getElementById('items-tbody');
    const tableHeader = document.getElementById('table-header');
    const addRowBtn = document.getElementById('add-row-btn');
    const removeRowBtn = document.getElementById('remove-row-btn');
    const addColBtn = document.getElementById('add-col-btn');
    const removeColBtn = document.getElementById('remove-col-btn');
    const signatureTriggerBtn = document.getElementById('signature-trigger-btn');
    const signatureInput = document.getElementById('signature-input');
    const signatureImage = document.getElementById('signature-image');
    const savePdfBtn = document.getElementById('save-pdf-btn');

    function updateFooterColspan() {
        const colCount = tableHeader.querySelector('tr').children.length;
        const footerCell = document.getElementById('table-footer-cell');
        if (footerCell) {
            footerCell.setAttribute('colspan', colCount);
        }
    }

    const { jsPDF } = window.jspdf;
    
    // Function to show a toast message
    function showToast(message, isError = false) {
        const toast = document.createElement('div');
        toast.className = `toast ${isError ? 'error' : 'success'}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('fade-out');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }
    
    // Add toast styles
    const style = document.createElement('style');
    style.textContent = `
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }
        .toast.fade-out {
            opacity: 0 !important;
            transform: translateY(-20px) !important;
        }
        .toast.success { background-color: #28a745; }
        .toast.error { background-color: #dc3545; }
    `;
    document.head.appendChild(style);
    
    // Function to show a modal for entering invoice name
    function showInvoiceNameModal() {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
            modal.style.display = 'flex';
            modal.style.justifyContent = 'center';
            modal.style.alignItems = 'center';
            modal.style.zIndex = '1000';
            
            const modalContent = document.createElement('div');
            modalContent.style.backgroundColor = 'white';
            modalContent.style.padding = '20px';
            modalContent.style.borderRadius = '8px';
            modalContent.style.width = '400px';
            modalContent.style.maxWidth = '90%';
            
            const title = document.createElement('h3');
            title.textContent = 'Enter Invoice Name';
            title.style.marginTop = '0';
            
            const input = document.createElement('input');
            input.type = 'text';
            input.id = 'invoice-name-input';
            input.placeholder = 'e.g., Invoice_2025_001';
            input.style.width = '100%';
            input.style.padding = '10px';
            input.style.margin = '10px 0';
            input.style.border = '1px solid #ddd';
            input.style.borderRadius = '4px';
            
            const refNumber = document.querySelector('.ref-number')?.textContent.trim() || '';
            const defaultName = `Invoice_${refNumber || 'Draft'}_${new Date().toISOString().slice(0, 10).replace(/-/g, '_')}`;
            input.value = defaultName;
            
            const buttonContainer = document.createElement('div');
            buttonContainer.style.display = 'flex';
            buttonContainer.style.justifyContent = 'flex-end';
            buttonContainer.style.gap = '10px';
            buttonContainer.style.marginTop = '15px';
            
            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = 'Cancel';
            cancelBtn.style.padding = '8px 16px';
            cancelBtn.style.border = '1px solid #ccc';
            cancelBtn.style.borderRadius = '4px';
            cancelBtn.style.background = 'white';
            cancelBtn.style.cursor = 'pointer';
            
            const saveBtn = document.createElement('button');
            saveBtn.textContent = 'Save PDF';
            saveBtn.style.padding = '8px 16px';
            saveBtn.style.border = '1px solid #4CAF50';
            saveBtn.style.borderRadius = '4px';
            saveBtn.style.background = '#4CAF50';
            saveBtn.style.color = 'white';
            saveBtn.style.cursor = 'pointer';
            
            buttonContainer.appendChild(cancelBtn);
            buttonContainer.appendChild(saveBtn);
            
            modalContent.appendChild(title);
            modalContent.appendChild(input);
            modalContent.appendChild(buttonContainer);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // Focus the input
            input.focus();
            input.select();
            
            // Handle Enter key press
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const value = input.value.trim();
                    if (value) {
                        document.body.removeChild(modal);
                        resolve(value);
                    }
                }
            });
            
            // Handle save button click
            saveBtn.onclick = function() {
                const value = input.value.trim();
                if (value) {
                    document.body.removeChild(modal);
                    resolve(value);
                } else {
                    alert('Please enter a valid invoice name');
                    input.focus();
                }
            };
            
            // Handle cancel button click
            cancelBtn.onclick = function() {
                document.body.removeChild(modal);
                resolve(null);
            };
            
            // Close on outside click
            modal.onclick = function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    resolve(null);
                }
            };
        });
    }
    
    // Main save function
    async function saveAsPdf() {
        // Show invoice name modal and wait for user input
        const invoiceName = await showInvoiceNameModal();
        if (!invoiceName) {
            showToast('PDF save cancelled', false, true);
            return;
        }
        
        const contentToSave = document.getElementById('content-to-save');
        const originalHtml = contentToSave.innerHTML;
        
        // Save button state
        const originalBtnText = savePdfBtn.innerHTML;
        savePdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        savePdfBtn.disabled = true;
        
        // Store original styles
        const originalStyles = {
            height: contentToSave.style.height,
            overflow: contentToSave.style.overflow,
            width: contentToSave.style.width,
            padding: contentToSave.style.padding,
            margin: contentToSave.style.margin
        };
        
        try {
            // Prepare the content for PDF generation
            contentToSave.style.width = '100%';
            contentToSave.style.padding = '0';
            contentToSave.style.margin = '0';
            contentToSave.style.height = 'auto';
            contentToSave.style.overflow = 'visible';
            
            // Hide controls and show loading state
            const controls = document.querySelectorAll('.controls');
            controls.forEach(ctrl => ctrl.style.display = 'none');
            
            // Use the user-provided invoice name with .pdf extension
            const fileName = `${invoiceName}.pdf`.replace(/[^a-z0-9\-_. ]/gi, '_');
            
            // Generate the PDF
            showToast('Generating PDF...');
            
            // Calculate scale based on window width for better PDF quality
            const scale = Math.min(1.5, window.innerWidth / 800);
            
            const canvas = await html2canvas(contentToSave, { 
                scale: scale,
                useCORS: true,
                allowTaint: true,
                logging: true,
                scrollX: 0,
                scrollY: 0,
                windowWidth: document.documentElement.offsetWidth,
                windowHeight: document.documentElement.offsetHeight,
                onclone: (clonedDoc) => {
                    // Ensure all content is visible in the clone
                    const clone = clonedDoc.getElementById('content-to-save');
                    clone.style.width = '100%';
                    clone.style.padding = '0';
                    clone.style.margin = '0';
                    clone.style.height = 'auto';
                    clone.style.overflow = 'visible';
                    
                    // Hide any UI elements that shouldn't appear in the PDF
                    const elementsToHide = clone.querySelectorAll('.controls, .no-print');
                    elementsToHide.forEach(el => el.style.display = 'none');
                    
                    // Ensure meta-info stays on one line
                    const metaInfo = clone.querySelector('.meta-info');
                    if (metaInfo) {
                        metaInfo.style.flexWrap = 'nowrap';
                        metaInfo.style.gap = '5px';
                    }
                }
            });
            
            // Create PDF
            showToast('Creating document...');
            const pdf = new jsPDF('p', 'mm', 'a4');
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const ratio = canvas.width / canvas.height;
            const imgHeight = pdfWidth / ratio;
            
            // Add first page
            pdf.addImage(canvas, 'PNG', 0, 0, pdfWidth, imgHeight, undefined, 'FAST');
            
            // Add additional pages if content is too long
            let heightLeft = imgHeight - pdfHeight;
            let position = 0;
            
            while (heightLeft > 0) {
                position = -pdfHeight + (heightLeft > 0 ? heightLeft : 0);
                pdf.addPage();
                pdf.addImage(canvas, 'PNG', 0, position, pdfWidth, imgHeight, undefined, 'FAST');
                heightLeft -= pdfHeight;
            }
            
            // Get PDF as data URL
            const pdfData = pdf.output('datauristring');
            
            // Validate PDF data
            if (!pdfData || pdfData.length < 100) { // Simple validation
                throw new Error('Generated PDF data is invalid or empty');
            }
            
            // Save to database
            showToast('Saving to database...');
            const response = await fetch('save_pdf.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    pdfData: pdfData,
                    fileName: fileName
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json().catch(err => {
                console.error('Failed to parse JSON response:', err);
                throw new Error('Invalid response from server');
            });
            
            if (!result.success) {
                throw new Error(result.message || 'Failed to save PDF to database');
            }
            
            // Download the PDF
            pdf.save(fileName);
            showToast('PDF saved successfully!');
            
            // Update UI with success state briefly
            savePdfBtn.innerHTML = '<i class="fas fa-check"></i> Saved!';
            setTimeout(() => {
                savePdfBtn.innerHTML = originalBtnText;
            }, 2000);
            
        } catch (err) {
            console.error('PDF Generation Error:', err);
            showToast(`Error: ${err.message || 'Failed to save PDF'}`, true);
            
            // Restore button state with error indication
            savePdfBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
            setTimeout(() => {
                savePdfBtn.innerHTML = originalBtnText;
            }, 3000);
            
            // Re-throw for debugging
            throw err;
            
        } finally {
            // Restore original state
            contentToSave.style.height = originalHeight;
            contentToSave.style.overflow = originalOverflow;
            
            // Re-enable the save button after a short delay
            setTimeout(() => {
                savePdfBtn.disabled = false;
            }, 1000);
            
            // Show controls again
            const controls = document.querySelectorAll('.controls');
            controls.forEach(ctrl => ctrl.style.visibility = 'visible');
        }
    }
    
    function updateSerialNumbers() { itemsTbody.querySelectorAll('tr').forEach((row, index) => { row.querySelector('.sn-cell').textContent = index + 1; }); }
    
    // --- CORRECTED addNewRow FUNCTION to include inline styles ---
    function addNewRow() {
        const newRow = document.createElement('tr');
        newRow.style.borderBottom = '1px solid #000'; // Add row border directly
        const colCount = tableHeader.querySelector('tr').children.length;
        
        // S.N. Cell
        const snCell = newRow.insertCell();
        snCell.className = 'sn-cell';
        snCell.style.borderLeft = 'none';

        // Other cells
        for (let i = 1; i < colCount; i++) {
            const newCell = newRow.insertCell();
            newCell.style.borderLeft = '1px solid #000'; // Add cell border
            const textarea = document.createElement('textarea');
            textarea.rows = 1;
            if (i === 1) textarea.className = 'description-field';
            newCell.appendChild(textarea);
        }
        itemsTbody.appendChild(newRow);
        updateSerialNumbers();
    }

    function removeLastRow() { if (itemsTbody.rows.length > 0) { itemsTbody.lastElementChild.remove(); } }
    
    // --- CORRECTED addColumn FUNCTION to include inline styles ---
    function addColumn() {
        const headerRow = tableHeader.querySelector('tr');
        const newTh = document.createElement('th');
        newTh.contentEditable = 'true';
        newTh.textContent = 'New Col';
        // Apply styles directly for PDF rendering
        Object.assign(newTh.style, {
            border: '1px solid #000',
            padding: '8px',
            textAlign: 'center',
            fontWeight: 'bold',
            backgroundColor: '#f2f2f2'
        });
        headerRow.appendChild(newTh);

        itemsTbody.querySelectorAll('tr').forEach(row => {
            const newCell = row.insertCell();
            newCell.style.borderLeft = '1px solid #000'; // Add border directly
            const textarea = document.createElement('textarea');
            textarea.rows = 1;
            newCell.appendChild(textarea);
        });
        updateFooterColspan();
    }

    function removeColumn() {
        const headerRow = tableHeader.querySelector('tr');
        if (headerRow.children.length > 2) {
            headerRow.lastElementChild.remove();
            itemsTbody.querySelectorAll('tr').forEach(row => row.lastElementChild.remove());
            updateFooterColspan();
        } else {
            alert("Cannot remove essential columns.");
        }
    }
    function autoGrowTextarea(element) { element.style.height = 'auto'; element.style.height = (element.scrollHeight) + 'px'; }
    itemsTbody.addEventListener('input', e => { if (e.target.tagName.toLowerCase() === 'textarea') autoGrowTextarea(e.target); });
    signatureTriggerBtn.addEventListener('click', () => { signatureInput.click(); });
    signatureInput.addEventListener('change', e => {
        if (e.target.files && e.target.files[0]) {
            const reader = new FileReader();
            reader.onload = (event) => { signatureImage.src = event.target.result; signatureImage.style.display = 'block'; };
            reader.readAsDataURL(e.target.files[0]);
        }
    });

    addTotalRowBtn.addEventListener('click', addTotalRow);
    removeTotalRowBtn.addEventListener('click', removeTotalRow);
    
    addRowBtn.addEventListener('click', addNewRow);
    removeRowBtn.addEventListener('click', removeLastRow);
    addColBtn.addEventListener('click', addColumn);
    removeColBtn.addEventListener('click', removeColumn);
    savePdfBtn.addEventListener('click', saveAsPdf);
    
    for(let i = 0; i < 5; i++) {
        addNewRow();
    }
    updateFooterColspan();

    // Function to add a new total row
    function addTotalRow() {
        const newRow = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = tableHeader.querySelector('tr').children.length; // Span all columns
        cell.contentEditable = true;
        cell.style.textAlign = 'center';
        cell.textContent = 'Total amount RO 0 /- (Rial Omani Zero rial-Only)';
        newRow.appendChild(cell);
        tableFooter.appendChild(newRow);
    }
    
    // Function to remove the last total row
    function removeTotalRow() {
        const rows = tableFooter.querySelectorAll('tr');
        if (rows.length > 1) { // Keep at least one row
            tableFooter.removeChild(rows[rows.length - 1]);
        } else {
            alert("At least one total row must remain.");
        }
    }
});
</script>

</body>
</html>
