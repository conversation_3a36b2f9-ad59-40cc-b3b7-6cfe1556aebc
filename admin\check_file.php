<?php
require_once 'config/config.php';

$pdo = getDbConnection();

// Get file ID from query string or default to 1
$file_id = $_GET['id'] ?? 1;

try {
    $stmt = $pdo->prepare("SELECT * FROM documents WHERE id = ?");
    $stmt->execute([$file_id]);
    $file = $stmt->fetch();
    
    echo "<h2>File Details (ID: $file_id)</h2>";
    if ($file) {
        echo "<pre>";
        print_r($file);
        echo "</pre>";
        
        // Check if file exists
        $file_path = __DIR__ . '/../' . $file['file_path'];
        echo "<p>Checking file path: " . htmlspecialchars($file_path) . "</p>";
        echo "<p>File exists: " . (file_exists($file_path) ? 'Yes' : 'No') . "</p>";
        if (file_exists($file_path)) {
            echo "<p>File size: " . filesize($file_path) . " bytes</p>";
        }
    } else {
        echo "<p>No file found with ID: $file_id</p>";
    }
    
    // List all files in the database
    echo "<h2>All Files in Database</h2>";
    $stmt = $pdo->query("SELECT id, file_name, file_path, file_size_kb, upload_date FROM documents ORDER BY id DESC");
    $files = $stmt->fetchAll();
    
    if (count($files) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>File Name</th><th>Path</th><th>Size (KB)</th><th>Uploaded</th></tr>";
        foreach ($files as $f) {
            echo "<tr>";
            echo "<td>{$f['id']}</td>";
            echo "<td>" . htmlspecialchars($f['file_name']) . "</td>";
            echo "<td>" . htmlspecialchars($f['file_path']) . "</td>";
            echo "<td>{$f['file_size_kb']}</td>";
            echo "<td>{$f['upload_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No files found in the database.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
