<?php
/**
 * Test the attendance API endpoints
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Testing Attendance API</h1>\n";

$today = date('Y-m-d');
$base_url = 'http://192.168.0.106/MtcInvoiceNewProject';

// Test 1: Get workers
echo "<h2>Test 1: Get Workers</h2>\n";
$workers_url = "$base_url/api/attendance/workers.php";
$workers_response = file_get_contents($workers_url);
echo "<p><strong>URL:</strong> $workers_url</p>\n";
echo "<p><strong>Response:</strong></p>\n";
echo "<pre>" . htmlspecialchars($workers_response) . "</pre>\n";

$workers_data = json_decode($workers_response, true);
if ($workers_data && $workers_data['success']) {
    echo "<p style='color: green;'>✅ Workers API working</p>\n";
    $workers_count = count($workers_data['data']['workers'] ?? []);
    echo "<p>Found $workers_count workers</p>\n";
} else {
    echo "<p style='color: red;'>❌ Workers API failed</p>\n";
}

// Test 2: Get attendance for today
echo "<h2>Test 2: Get Attendance for Today ($today)</h2>\n";
$attendance_url = "$base_url/api/attendance/attendance.php/date/$today";
$attendance_response = file_get_contents($attendance_url);
echo "<p><strong>URL:</strong> $attendance_url</p>\n";
echo "<p><strong>Response:</strong></p>\n";
echo "<pre>" . htmlspecialchars($attendance_response) . "</pre>\n";

$attendance_data = json_decode($attendance_response, true);
if ($attendance_data && $attendance_data['success']) {
    echo "<p style='color: green;'>✅ Attendance API working</p>\n";
    $workers_count = count($attendance_data['data']['workers'] ?? []);
    echo "<p>Found $workers_count workers for attendance</p>\n";
    
    if ($workers_count > 0) {
        echo "<h3>Sample Workers:</h3>\n";
        echo "<ul>\n";
        $sample_workers = array_slice($attendance_data['data']['workers'], 0, 3);
        foreach ($sample_workers as $worker) {
            echo "<li>{$worker['name']} (ID: {$worker['employee_id']}, Status: {$worker['status']})</li>\n";
        }
        echo "</ul>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Attendance API failed</p>\n";
}

// Test 3: Test bulk attendance creation
echo "<h2>Test 3: Test Bulk Attendance Creation</h2>\n";

if (isset($attendance_data['data']['workers']) && count($attendance_data['data']['workers']) > 0) {
    // Prepare sample attendance data
    $sample_workers = array_slice($attendance_data['data']['workers'], 0, 2);
    $bulk_data = [
        'attendance_date' => $today,
        'workers' => []
    ];
    
    foreach ($sample_workers as $index => $worker) {
        $bulk_data['workers'][] = [
            'worker_id' => $worker['id'],
            'status' => 'present',
            'check_in_time' => '09:00',
            'check_out_time' => '17:00',
            'notes' => 'Test attendance record'
        ];
    }
    
    echo "<p><strong>Sending bulk attendance data:</strong></p>\n";
    echo "<pre>" . htmlspecialchars(json_encode($bulk_data, JSON_PRETTY_PRINT)) . "</pre>\n";
    
    // Send POST request
    $bulk_url = "$base_url/api/attendance/attendance.php/bulk";
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($bulk_data)
        ]
    ]);
    
    $bulk_response = file_get_contents($bulk_url, false, $context);
    echo "<p><strong>Bulk Response:</strong></p>\n";
    echo "<pre>" . htmlspecialchars($bulk_response) . "</pre>\n";
    
    $bulk_result = json_decode($bulk_response, true);
    if ($bulk_result && $bulk_result['success']) {
        echo "<p style='color: green;'>✅ Bulk attendance creation working</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Bulk attendance creation failed</p>\n";
    }
} else {
    echo "<p style='color: orange;'>⚠️ No workers available for bulk attendance test</p>\n";
}

echo "<h2>Summary</h2>\n";
echo "<p><a href='admin/attendance.php' style='color: blue;'>Go to Attendance Page</a></p>\n";
?>
