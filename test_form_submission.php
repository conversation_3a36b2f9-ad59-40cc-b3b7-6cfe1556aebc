<?php
/**
 * Test form submission to verify attendance functionality
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'admin/config/config.php';

echo "<h1>Testing Form Submission</h1>\n";

$today = date('Y-m-d');

try {
    // Step 1: Get workers from API
    echo "<h2>Step 1: Getting Workers</h2>\n";
    $workers_response = makeApiRequest('/api/attendance/attendance.php/date/' . $today);
    
    echo "<p><strong>Workers API Response:</strong></p>\n";
    echo "<pre>" . htmlspecialchars(print_r($workers_response, true)) . "</pre>\n";
    
    if (!$workers_response['success']) {
        echo "<p style='color: red;'>❌ Failed to get workers</p>\n";
        exit;
    }
    
    // Extract workers data correctly
    $api_data = $workers_response['data']['data'] ?? $workers_response['data'];
    $workers = $api_data['workers'] ?? [];
    
    echo "<p style='color: green;'>✅ Found " . count($workers) . " workers</p>\n";
    
    if (empty($workers)) {
        echo "<p style='color: red;'>❌ No workers found to test with</p>\n";
        exit;
    }
    
    // Step 2: Prepare form data
    echo "<h2>Step 2: Preparing Form Data</h2>\n";
    
    $form_data = [
        'action' => 'mark_attendance',
        'attendance_date' => $today,
        'workers' => []
    ];
    
    // Take first 3 workers for testing
    $test_workers = array_slice($workers, 0, 3);
    foreach ($test_workers as $index => $worker) {
        $form_data['workers'][] = [
            'worker_id' => $worker['id'],
            'status' => $index == 0 ? 'present' : ($index == 1 ? 'late' : 'absent'),
            'check_in_time' => $index == 0 ? '09:00' : ($index == 1 ? '09:30' : ''),
            'check_out_time' => $index == 0 ? '17:00' : ($index == 1 ? '17:00' : ''),
            'notes' => "Test attendance for worker " . $worker['name']
        ];
    }
    
    echo "<p><strong>Form Data to Submit:</strong></p>\n";
    echo "<pre>" . htmlspecialchars(print_r($form_data, true)) . "</pre>\n";
    
    // Step 3: Submit to bulk API
    echo "<h2>Step 3: Submitting to Bulk API</h2>\n";
    
    $bulk_response = makeApiRequest('/api/attendance/attendance.php/bulk', 'POST', [
        'attendance_date' => $form_data['attendance_date'],
        'workers' => $form_data['workers']
    ]);
    
    echo "<p><strong>Bulk API Response:</strong></p>\n";
    echo "<pre>" . htmlspecialchars(print_r($bulk_response, true)) . "</pre>\n";
    
    if ($bulk_response['success'] && isset($bulk_response['data']['successful'])) {
        $successful = $bulk_response['data']['successful'];
        $failed = $bulk_response['data']['failed'];
        echo "<p style='color: green;'>✅ Bulk submission successful: $successful created, $failed failed</p>\n";
        
        if ($failed > 0) {
            echo "<p style='color: orange;'>⚠️ Some records failed:</p>\n";
            echo "<ul>\n";
            foreach ($bulk_response['data']['errors'] as $error) {
                echo "<li>" . htmlspecialchars($error) . "</li>\n";
            }
            echo "</ul>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Bulk submission failed</p>\n";
        if (isset($bulk_response['data']['message'])) {
            echo "<p>Error: " . htmlspecialchars($bulk_response['data']['message']) . "</p>\n";
        }
    }
    
    // Step 4: Verify data was saved
    echo "<h2>Step 4: Verifying Data Was Saved</h2>\n";
    
    $verification_response = makeApiRequest('/api/attendance/attendance.php/date/' . $today);
    
    if ($verification_response['success']) {
        $verify_data = $verification_response['data']['data'] ?? $verification_response['data'];
        $verify_workers = $verify_data['workers'] ?? [];
        
        echo "<p style='color: green;'>✅ Verification successful</p>\n";
        echo "<p><strong>Workers with attendance data:</strong></p>\n";
        echo "<ul>\n";
        
        $workers_with_attendance = 0;
        foreach ($verify_workers as $worker) {
            if (isset($worker['attendance_id']) && $worker['attendance_id']) {
                $workers_with_attendance++;
                echo "<li>{$worker['name']} - Status: {$worker['status']}, Check-in: {$worker['check_in_time']}</li>\n";
            }
        }
        echo "</ul>\n";
        
        echo "<p><strong>Summary:</strong> $workers_with_attendance workers have attendance records for today</p>\n";
        
        if ($workers_with_attendance > 0) {
            echo "<p style='color: green; font-weight: bold;'>🎉 SUCCESS: Attendance functionality is working!</p>\n";
        } else {
            echo "<p style='color: red;'>❌ No attendance records found after submission</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to verify saved data</p>\n";
    }
    
    echo "<h2>Test Complete</h2>\n";
    echo "<p><a href='admin/attendance.php?date=$today' style='color: blue;'>View Attendance Page</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>
