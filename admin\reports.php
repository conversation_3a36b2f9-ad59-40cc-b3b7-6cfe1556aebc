<?php
/**
 * Attendance Reports
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require admin role
requireAdmin();

$current_page = 'reports';
$page_title = 'Attendance Reports';

// Get report parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');
$department = $_GET['department'] ?? '';
$report_type = $_GET['report_type'] ?? 'summary';

// Get attendance report data
try {
    $query_params = [
        'start_date' => $start_date,
        'end_date' => $end_date
    ];
    
    if ($department) {
        $query_params['department'] = $department;
    }
    
    $query_string = http_build_query($query_params);
    
    $report_response = makeApiRequest("/api/attendance/attendance.php/report?{$query_string}");
    $report_data = $report_response['data'] ?? [];
    
    $worker_stats = $report_data['worker_statistics'] ?? [];
    $overall_stats = $report_data['overall_statistics'] ?? [];
    $department_stats = $report_data['department_statistics'] ?? [];
    
    // Get departments for filter
    $workers_response = makeApiRequest('/api/attendance/workers.php');
    $workers_data = $workers_response['data'] ?? [];
    $workers = $workers_data['workers'] ?? [];
    $departments = array_unique(array_filter(array_column($workers, 'department')));
    sort($departments);
    
} catch (Exception $e) {
    error_log("Reports page error: " . $e->getMessage());
    $worker_stats = [];
    $overall_stats = [];
    $department_stats = [];
    $departments = [];
}

include 'includes/header.php';
?>

<!-- Reports Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Attendance Reports</h1>
        <p class="text-muted">Generate and view attendance reports and analytics</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <button class="btn btn-primary" onclick="exportReport('pdf')">
                <i class="bi bi-file-pdf me-2"></i>
                Export PDF
            </button>
            <button class="btn btn-outline-primary" onclick="exportReport('excel')">
                <i class="bi bi-file-excel me-2"></i>
                Export Excel
            </button>
        </div>
    </div>
</div>

<!-- Report Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">Department</label>
                <select class="form-select" id="department" name="department">
                    <option value="">All Departments</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?= htmlspecialchars($dept) ?>" <?= $department === $dept ? 'selected' : '' ?>>
                            <?= htmlspecialchars($dept) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>Generate Report
                </button>
                <a href="reports.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Overall Statistics -->
<?php if (!empty($overall_stats)): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Workers</h6>
                        <h3 class="mb-0"><?= $overall_stats['total_workers'] ?? 0 ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Present</h6>
                        <h3 class="mb-0"><?= $overall_stats['total_present'] ?? 0 ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Absent</h6>
                        <h3 class="mb-0"><?= $overall_stats['total_absent'] ?? 0 ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-x-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Attendance Rate</h6>
                        <h3 class="mb-0"><?= $overall_stats['overall_attendance_percentage'] ?? 0 ?>%</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-graph-up" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Department Statistics -->
<?php if (!empty($department_stats)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-building me-2"></i>
            Department-wise Statistics
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Workers</th>
                        <th>Present Days</th>
                        <th>Absent Days</th>
                        <th>Late Days</th>
                        <th>Half Days</th>
                        <th>Attendance Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($department_stats as $dept): ?>
                        <tr>
                            <td>
                                <span class="badge bg-info"><?= htmlspecialchars($dept['department']) ?></span>
                            </td>
                            <td><?= $dept['workers_count'] ?></td>
                            <td>
                                <span class="badge bg-success"><?= $dept['present_count'] ?></span>
                            </td>
                            <td>
                                <span class="badge bg-danger"><?= $dept['absent_count'] ?></span>
                            </td>
                            <td>
                                <span class="badge bg-warning"><?= $dept['late_count'] ?></span>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= $dept['half_day_count'] ?></span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 100px; height: 8px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: <?= $dept['dept_attendance_percentage'] ?>%"></div>
                                    </div>
                                    <span class="fw-medium"><?= $dept['dept_attendance_percentage'] ?>%</span>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Worker Statistics -->
<?php if (!empty($worker_stats)): ?>
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-person-badge me-2"></i>
            Worker-wise Statistics
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="workerStatsTable">
                <thead>
                    <tr>
                        <th>Worker</th>
                        <th>Department</th>
                        <th>Total Days</th>
                        <th>Present</th>
                        <th>Absent</th>
                        <th>Late</th>
                        <th>Half Days</th>
                        <th>Overtime (hrs)</th>
                        <th>Attendance Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($worker_stats as $worker): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-3">
                                        <?= strtoupper(substr($worker['name'], 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div class="fw-medium"><?= htmlspecialchars($worker['name']) ?></div>
                                        <small class="text-muted"><?= htmlspecialchars($worker['employee_id']) ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php if ($worker['department']): ?>
                                    <span class="badge bg-info"><?= htmlspecialchars($worker['department']) ?></span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td><?= $worker['total_days'] ?></td>
                            <td>
                                <span class="badge bg-success"><?= $worker['present_days'] ?></span>
                            </td>
                            <td>
                                <span class="badge bg-danger"><?= $worker['absent_days'] ?></span>
                            </td>
                            <td>
                                <span class="badge bg-warning"><?= $worker['late_days'] ?></span>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= $worker['half_days'] ?></span>
                            </td>
                            <td><?= number_format($worker['total_overtime'] ?? 0, 1) ?></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 80px; height: 8px;">
                                        <div class="progress-bar bg-<?= $worker['attendance_percentage'] >= 90 ? 'success' : ($worker['attendance_percentage'] >= 75 ? 'warning' : 'danger') ?>" 
                                             role="progressbar" style="width: <?= $worker['attendance_percentage'] ?>%"></div>
                                    </div>
                                    <span class="fw-medium text-<?= $worker['attendance_percentage'] >= 90 ? 'success' : ($worker['attendance_percentage'] >= 75 ? 'warning' : 'danger') ?>">
                                        <?= $worker['attendance_percentage'] ?>%
                                    </span>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Reports JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date range to current month if not set
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');

    if (!startDate.value) {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        startDate.value = firstDay.toISOString().split('T')[0];
    }

    if (!endDate.value) {
        const now = new Date();
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        endDate.value = lastDay.toISOString().split('T')[0];
    }

    // Add sorting to worker stats table
    const table = document.getElementById('workerStatsTable');
    if (table) {
        makeTableSortable(table);
    }
});

// Export report function
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    params.set('export', '1');

    const url = `/api/attendance/export.php?${params.toString()}`;
    window.open(url, '_blank');
}

// Quick date range functions
function setDateRange(range) {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    const now = new Date();

    switch (range) {
        case 'today':
            startDate.value = now.toISOString().split('T')[0];
            endDate.value = now.toISOString().split('T')[0];
            break;
        case 'week':
            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
            const weekEnd = new Date(now.setDate(weekStart.getDate() + 6));
            startDate.value = weekStart.toISOString().split('T')[0];
            endDate.value = weekEnd.toISOString().split('T')[0];
            break;
        case 'month':
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            startDate.value = monthStart.toISOString().split('T')[0];
            endDate.value = monthEnd.toISOString().split('T')[0];
            break;
        case 'quarter':
            const quarter = Math.floor(now.getMonth() / 3);
            const quarterStart = new Date(now.getFullYear(), quarter * 3, 1);
            const quarterEnd = new Date(now.getFullYear(), quarter * 3 + 3, 0);
            startDate.value = quarterStart.toISOString().split('T')[0];
            endDate.value = quarterEnd.toISOString().split('T')[0];
            break;
        case 'year':
            const yearStart = new Date(now.getFullYear(), 0, 1);
            const yearEnd = new Date(now.getFullYear(), 11, 31);
            startDate.value = yearStart.toISOString().split('T')[0];
            endDate.value = yearEnd.toISOString().split('T')[0];
            break;
    }
}

// Add quick date range buttons
document.addEventListener('DOMContentLoaded', function() {
    const filterCard = document.querySelector('.card .card-body form');
    if (filterCard) {
        const quickRangeDiv = document.createElement('div');
        quickRangeDiv.className = 'col-12 mt-3';
        quickRangeDiv.innerHTML = `
            <label class="form-label">Quick Date Ranges:</label>
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('today')">Today</button>
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('week')">This Week</button>
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('month')">This Month</button>
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('quarter')">This Quarter</button>
                <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('year')">This Year</button>
            </div>
        `;

        const row = filterCard.querySelector('.row');
        row.appendChild(quickRangeDiv);
    }
});

// Simple table sorting function
function makeTableSortable(table) {
    const headers = table.querySelectorAll('thead th');
    headers.forEach((header, index) => {
        if (index === 0) return; // Skip first column (worker info)

        header.style.cursor = 'pointer';
        header.innerHTML += ' <i class="bi bi-arrow-down-up text-muted"></i>';

        header.addEventListener('click', function() {
            sortTable(table, index);
        });
    });
}

function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    const isNumeric = rows.every(row => {
        const cell = row.cells[columnIndex];
        const text = cell.textContent.trim().replace(/[^\d.-]/g, '');
        return !isNaN(text) && text !== '';
    });

    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();

        if (isNumeric) {
            const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
            const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));
            return bNum - aNum; // Descending order for numbers
        } else {
            return aText.localeCompare(bText);
        }
    });

    rows.forEach(row => tbody.appendChild(row));
}
</script>

<?php include 'includes/footer.php'; ?>
