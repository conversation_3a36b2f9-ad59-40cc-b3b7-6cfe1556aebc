<?php
/**
 * Test API directly to see exact response
 */

echo "<h1>Test API Direct Call</h1>\n";

$today = date('Y-m-d');
$api_url = "http://192.168.0.106/MtcInvoiceNewProject/api/attendance/attendance.php?action=date&date=$today";

echo "<p><strong>Testing URL:</strong> $api_url</p>\n";

// Test with file_get_contents
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = file_get_contents($api_url, false, $context);
$http_code = 200;

if (isset($http_response_header)) {
    foreach ($http_response_header as $header) {
        if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
            $http_code = intval($matches[1]);
            break;
        }
    }
}

echo "<p><strong>HTTP Code:</strong> $http_code</p>\n";
echo "<p><strong>Raw Response:</strong></p>\n";
echo "<pre>" . htmlspecialchars($response) . "</pre>\n";

$json_data = json_decode($response, true);
if ($json_data) {
    echo "<p style='color: green;'>✅ Valid JSON</p>\n";
    
    if (isset($json_data['success']) && $json_data['success']) {
        echo "<p style='color: green;'>✅ API Success</p>\n";
        
        if (isset($json_data['data']['workers'])) {
            $workers = $json_data['data']['workers'];
            echo "<p><strong>Workers found:</strong> " . count($workers) . "</p>\n";
            
            if (count($workers) > 0) {
                echo "<h3>Workers:</h3>\n";
                echo "<ul>\n";
                foreach ($workers as $worker) {
                    echo "<li>ID: {$worker['id']}, Name: {$worker['name']}</li>\n";
                }
                echo "</ul>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ No 'workers' key in response</p>\n";
            echo "<p><strong>Available keys:</strong> " . implode(', ', array_keys($json_data['data'] ?? [])) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ API Failed</p>\n";
        echo "<p><strong>Message:</strong> " . ($json_data['message'] ?? 'No message') . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Invalid JSON</p>\n";
}

// Also test with makeApiRequest
echo "<h2>Test with makeApiRequest</h2>\n";

require_once 'admin/config/config.php';

$api_response = makeApiRequest("/attendance/attendance.php?action=date&date=$today");

echo "<p><strong>makeApiRequest Success:</strong> " . ($api_response['success'] ? 'Yes' : 'No') . "</p>\n";
echo "<p><strong>makeApiRequest HTTP Code:</strong> {$api_response['http_code']}</p>\n";
echo "<p><strong>makeApiRequest Response:</strong></p>\n";
echo "<pre>" . htmlspecialchars(print_r($api_response, true)) . "</pre>\n";

// Test database connection directly
echo "<h2>Test Database Connection</h2>\n";

try {
    require_once 'api/config/config.php';
    $database = new Database();
    $pdo = $database->getConnection();
    
    if ($pdo) {
        echo "<p style='color: green;'>✅ Database connected</p>\n";
        
        $stmt = $pdo->prepare("
            SELECT id, employee_id, name, department, position, 'not_marked' as status
            FROM workers 
            WHERE deleted_at IS NULL AND status = 'active'
            ORDER BY name ASC
        ");
        $stmt->execute();
        $workers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Direct DB query result:</strong> " . count($workers) . " workers</p>\n";
        
        if (count($workers) > 0) {
            echo "<ul>\n";
            foreach ($workers as $worker) {
                echo "<li>ID: {$worker['id']}, Name: {$worker['name']}</li>\n";
            }
            echo "</ul>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>\n";
}
?>
