<?php
/**
 * Workers API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/workers.php' from segments
$segments = array_slice($segments, 3);

// Handle query parameters as fallback for clean URLs
$worker_id = $_GET['worker_id'] ?? null;

// If we have query parameters, use them to simulate clean URL segments
if ($worker_id && empty($segments[0])) {
    $segments = [$worker_id];
}

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Validate required fields
 */
function validateRequired($data, $fields) {
    $missing = [];
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// Handle the request based on method and endpoint
try {
    switch ($method) {
        case 'GET':
            // Get single worker or list
            if (isset($segments[0]) && is_numeric($segments[0])) {
                getWorker($segments[0]);
            } else {
                getWorkers();
            }
            break;
            
        case 'POST':
            // Create new worker
            createWorker();
            break;
            
        case 'PUT':
        case 'PATCH':
            // Update existing worker
            if (isset($segments[0]) && is_numeric($segments[0])) {
                updateWorker($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Worker ID is required for update');
            }
            break;
            
        case 'DELETE':
            // Delete worker (soft delete)
            if (isset($segments[0]) && is_numeric($segments[0])) {
                deleteWorker($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Worker ID is required for deletion');
            }
            break;
            
        default:
            sendJsonResponse(405, null, 'Method not allowed');
    }
} catch (Exception $e) {
    error_log("Workers API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get all workers with optional filtering
 */
function getWorkers() {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        // Build query with filters
        $where = ['deleted_at IS NULL'];
        $params = [];
        
        // Filter by status
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $where[] = 'status = ?';
            $params[] = $_GET['status'];
        }
        
        // Filter by department
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $where[] = 'department = ?';
            $params[] = $_GET['department'];
        }
        
        // Search by name or employee_id
        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $where[] = '(name LIKE ? OR employee_id LIKE ?)';
            $searchTerm = '%' . $_GET['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Pagination
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 20;
        $offset = ($page - 1) * $limit;
        
        // Count total records
        $countSql = "SELECT COUNT(*) as total FROM workers WHERE " . implode(' AND ', $where);
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];
        
        // Get workers
        $sql = "SELECT id, employee_id, name, email, phone, department, position, 
                       hire_date, status, created_at, updated_at 
                FROM workers 
                WHERE " . implode(' AND ', $where) . "
                ORDER BY name ASC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $workers = $stmt->fetchAll();
        
        sendJsonResponse(200, [
            'workers' => $workers,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Get workers error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve workers');
    }
}

/**
 * Get single worker by ID
 */
function getWorker($id) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        $stmt = $pdo->prepare("
            SELECT id, employee_id, name, email, phone, department, position, 
                   hire_date, status, created_at, updated_at 
            FROM workers 
            WHERE id = ? AND deleted_at IS NULL
        ");
        $stmt->execute([$id]);
        $worker = $stmt->fetch();
        
        if (!$worker) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }
        
        sendJsonResponse(200, $worker);
        
    } catch (Exception $e) {
        error_log("Get worker error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve worker');
    }
}

/**
 * Create new worker
 */
function createWorker() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided');
            return;
        }
        
        // Validate required fields
        $required = ['employee_id', 'name'];
        $missing = validateRequired($data, $required);
        if (!empty($missing)) {
            sendJsonResponse(400, null, 'Missing required fields: ' . implode(', ', $missing));
            return;
        }
        
        // Sanitize input
        $data = sanitizeInput($data);
        
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        // Check if employee_id already exists
        $stmt = $pdo->prepare("SELECT id FROM workers WHERE employee_id = ? AND deleted_at IS NULL");
        $stmt->execute([$data['employee_id']]);
        if ($stmt->fetch()) {
            sendJsonResponse(409, null, 'Employee ID already exists');
            return;
        }
        
        // Insert new worker
        $stmt = $pdo->prepare("
            INSERT INTO workers (employee_id, name, email, phone, department, position, hire_date, status, created_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data['employee_id'],
            $data['name'],
            $data['email'] ?? null,
            $data['phone'] ?? null,
            $data['department'] ?? null,
            $data['position'] ?? null,
            $data['hire_date'] ?? null,
            $data['status'] ?? 'active',
            1 // TODO: Get from authenticated user
        ]);
        
        $workerId = $pdo->lastInsertId();
        
        // Get the created worker
        $stmt = $pdo->prepare("
            SELECT id, employee_id, name, email, phone, department, position, 
                   hire_date, status, created_at, updated_at 
            FROM workers 
            WHERE id = ?
        ");
        $stmt->execute([$workerId]);
        $worker = $stmt->fetch();
        
        sendJsonResponse(201, $worker, 'Worker created successfully');
        
    } catch (Exception $e) {
        error_log("Create worker error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to create worker');
    }
}

/**
 * Update existing worker
 */
function updateWorker($id) {
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided for update');
            return;
        }

        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Check if worker exists
        $stmt = $pdo->prepare("SELECT id FROM workers WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }

        // Sanitize input
        $data = sanitizeInput($data);

        // Check if employee_id already exists (excluding current worker)
        if (isset($data['employee_id'])) {
            $stmt = $pdo->prepare("SELECT id FROM workers WHERE employee_id = ? AND id != ? AND deleted_at IS NULL");
            $stmt->execute([$data['employee_id'], $id]);
            if ($stmt->fetch()) {
                sendJsonResponse(409, null, 'Employee ID already exists');
                return;
            }
        }

        // Prepare the SQL query
        $updates = [];
        $values = [];

        $allowedFields = [
            'employee_id', 'name', 'email', 'phone', 'department',
            'position', 'hire_date', 'status'
        ];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updates[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($updates)) {
            sendJsonResponse(400, null, 'No valid fields provided for update');
            return;
        }

        $values[] = $id;

        $sql = "UPDATE workers SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        // Get the updated worker
        $stmt = $pdo->prepare("
            SELECT id, employee_id, name, email, phone, department, position,
                   hire_date, status, created_at, updated_at
            FROM workers
            WHERE id = ?
        ");
        $stmt->execute([$id]);
        $worker = $stmt->fetch();

        sendJsonResponse(200, $worker, 'Worker updated successfully');

    } catch (Exception $e) {
        error_log("Update worker error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to update worker');
    }
}

/**
 * Soft delete worker
 */
function deleteWorker($id) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Check if worker exists
        $stmt = $pdo->prepare("SELECT id FROM workers WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }

        // Check if worker has attendance records
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM worker_attendance WHERE worker_id = ?");
        $stmt->execute([$id]);
        $attendanceCount = $stmt->fetch()['count'];

        if ($attendanceCount > 0) {
            // Soft delete - mark as deleted but keep data for historical purposes
            $stmt = $pdo->prepare("UPDATE workers SET deleted_at = NOW(), status = 'terminated' WHERE id = ?");
            $stmt->execute([$id]);
            sendJsonResponse(200, null, 'Worker archived successfully (has attendance records)');
        } else {
            // Hard delete if no attendance records
            $stmt = $pdo->prepare("DELETE FROM workers WHERE id = ?");
            $stmt->execute([$id]);
            sendJsonResponse(200, null, 'Worker deleted successfully');
        }

    } catch (Exception $e) {
        error_log("Delete worker error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to delete worker');
    }
}
