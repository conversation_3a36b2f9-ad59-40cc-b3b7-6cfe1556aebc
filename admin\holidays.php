<?php
/**
 * Holiday Management
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require admin role
requireAdmin();

$current_page = 'holidays';
$page_title = 'Holiday Management';

// Handle holiday actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'delete' && isset($_POST['holiday_id'])) {
            $holiday_id = (int)$_POST['holiday_id'];
            
            $response = makeApiRequest("/api/attendance/holidays.php/{$holiday_id}", 'DELETE');
            
            if ($response && $response['success']) {
                showAlert('Holiday deleted successfully', 'success');
            } else {
                showAlert($response['message'] ?? 'Failed to delete holiday', 'danger');
            }
        }
        
    } catch (Exception $e) {
        showAlert($e->getMessage(), 'danger');
    }
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Get holidays
try {
    $year = $_GET['year'] ?? date('Y');
    $type = $_GET['type'] ?? '';
    $status = $_GET['status'] ?? '';
    
    $query_params = [];
    if ($year) $query_params['year'] = $year;
    if ($type) $query_params['type'] = $type;
    if ($status) $query_params['status'] = $status;
    
    $query_string = !empty($query_params) ? '?' . http_build_query($query_params) : '';
    
    $holidays_response = makeApiRequest("/api/attendance/holidays.php{$query_string}");
    $holidays_data = $holidays_response['data'] ?? [];
    $holidays = $holidays_data['holidays'] ?? [];
    
    // Get holiday statistics
    $total_holidays = count($holidays);
    $public_holidays = count(array_filter($holidays, function($h) { return $h['type'] === 'public'; }));
    $company_holidays = count(array_filter($holidays, function($h) { return $h['type'] === 'company'; }));
    $active_holidays = count(array_filter($holidays, function($h) { return $h['status'] === 'active'; }));
    
} catch (Exception $e) {
    error_log("Holidays page error: " . $e->getMessage());
    $holidays = [];
    $total_holidays = 0;
    $public_holidays = 0;
    $company_holidays = 0;
    $active_holidays = 0;
}

include 'includes/header.php';
?>

<!-- Holiday Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Holiday Management</h1>
        <p class="text-muted">Manage company holidays and public holidays</p>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
            <i class="bi bi-calendar-plus me-2"></i>
            Add New Holiday
        </button>
    </div>
</div>

<!-- Holiday Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Holidays</h6>
                        <h3 class="mb-0"><?= $total_holidays ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-calendar-event" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Public Holidays</h6>
                        <h3 class="mb-0"><?= $public_holidays ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-flag" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Company Holidays</h6>
                        <h3 class="mb-0"><?= $company_holidays ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-building" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Active Holidays</h6>
                        <h3 class="mb-0"><?= $active_holidays ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="year" class="form-label">Year</label>
                <select class="form-select" id="year" name="year">
                    <?php for ($y = date('Y') - 2; $y <= date('Y') + 2; $y++): ?>
                        <option value="<?= $y ?>" <?= $year == $y ? 'selected' : '' ?>><?= $y ?></option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">Type</label>
                <select class="form-select" id="type" name="type">
                    <option value="">All Types</option>
                    <option value="public" <?= $type === 'public' ? 'selected' : '' ?>>Public Holiday</option>
                    <option value="company" <?= $type === 'company' ? 'selected' : '' ?>>Company Holiday</option>
                    <option value="religious" <?= $type === 'religious' ? 'selected' : '' ?>>Religious Holiday</option>
                    <option value="national" <?= $type === 'national' ? 'selected' : '' ?>>National Holiday</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>Filter
                </button>
                <a href="holidays.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Holidays Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-calendar-event me-2"></i>
            Holidays List
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($holidays)): ?>
            <div class="text-center py-4">
                <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">No holidays found</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
                    <i class="bi bi-calendar-plus me-2"></i>
                    Add First Holiday
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Holiday Name</th>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Recurring</th>
                            <th>Status</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($holidays as $holiday): ?>
                            <tr>
                                <td>
                                    <div class="fw-medium"><?= htmlspecialchars($holiday['name']) ?></div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?= formatDate($holiday['holiday_date'], 'M j, Y') ?>
                                    </span>
                                    <br>
                                    <small class="text-muted"><?= formatDate($holiday['holiday_date'], 'l') ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $holiday['type'] === 'public' ? 'success' : ($holiday['type'] === 'company' ? 'warning' : 'info') ?>">
                                        <?= ucfirst($holiday['type']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($holiday['is_recurring']): ?>
                                        <span class="badge bg-info">
                                            <i class="bi bi-arrow-repeat me-1"></i>Recurring
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">One-time</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $holiday['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst($holiday['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($holiday['description']): ?>
                                        <span title="<?= htmlspecialchars($holiday['description']) ?>">
                                            <?= htmlspecialchars(substr($holiday['description'], 0, 50)) ?>
                                            <?= strlen($holiday['description']) > 50 ? '...' : '' ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="editHoliday(<?= htmlspecialchars(json_encode($holiday)) ?>)" 
                                                title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteHoliday(<?= $holiday['id'] ?>, '<?= htmlspecialchars($holiday['name']) ?>')" 
                                                title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Holiday Modal -->
<div class="modal fade" id="addHolidayModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Holiday</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addHolidayForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Holiday Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="holiday_date" class="form-label">Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="holiday_date" name="holiday_date" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="public">Public Holiday</option>
                                    <option value="company">Company Holiday</option>
                                    <option value="religious">Religious Holiday</option>
                                    <option value="national">National Holiday</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring" value="1">
                            <label class="form-check-label" for="is_recurring">
                                Recurring Holiday (repeats every year)
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Optional description about the holiday"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Holiday</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Holiday Modal -->
<div class="modal fade" id="editHolidayModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Holiday</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editHolidayForm">
                <input type="hidden" id="edit_holiday_id" name="holiday_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Holiday Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_holiday_date" class="form-label">Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="edit_holiday_date" name="holiday_date" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_type" class="form-label">Type</label>
                                <select class="form-select" id="edit_type" name="type">
                                    <option value="public">Public Holiday</option>
                                    <option value="company">Company Holiday</option>
                                    <option value="religious">Religious Holiday</option>
                                    <option value="national">National Holiday</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_status" class="form-label">Status</label>
                                <select class="form-select" id="edit_status" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_recurring" name="is_recurring" value="1">
                            <label class="form-check-label" for="edit_is_recurring">
                                Recurring Holiday (repeats every year)
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"
                                  placeholder="Optional description about the holiday"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Holiday</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Holiday management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add holiday form submission
    document.getElementById('addHolidayForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Convert checkbox to boolean
        data.is_recurring = formData.has('is_recurring') ? 1 : 0;

        fetch('/api/attendance/holidays.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('Holiday created successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addHolidayModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(result.message || 'Failed to create holiday', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while creating holiday', 'error');
        });
    });

    // Edit holiday form submission
    document.getElementById('editHolidayForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const holidayId = formData.get('holiday_id');
        const data = Object.fromEntries(formData);
        delete data.holiday_id;

        // Convert checkbox to boolean
        data.is_recurring = formData.has('is_recurring') ? 1 : 0;

        fetch(`/api/attendance/holidays.php/${holidayId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('Holiday updated successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('editHolidayModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(result.message || 'Failed to update holiday', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while updating holiday', 'error');
        });
    });
});

// Edit holiday function
function editHoliday(holiday) {
    document.getElementById('edit_holiday_id').value = holiday.id;
    document.getElementById('edit_name').value = holiday.name || '';
    document.getElementById('edit_holiday_date').value = holiday.holiday_date || '';
    document.getElementById('edit_type').value = holiday.type || 'public';
    document.getElementById('edit_status').value = holiday.status || 'active';
    document.getElementById('edit_is_recurring').checked = holiday.is_recurring == 1;
    document.getElementById('edit_description').value = holiday.description || '';

    new bootstrap.Modal(document.getElementById('editHolidayModal')).show();
}

// Delete holiday
function deleteHoliday(holidayId, holidayName) {
    if (confirm(`Are you sure you want to delete holiday "${holidayName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="holiday_id" value="${holidayId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
