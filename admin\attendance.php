<?php
/**
 * Attendance Management
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require admin role
requireAdmin();

$current_page = 'attendance';
$page_title = 'Attendance Management';

// Get current date or selected date
$selected_date = $_GET['date'] ?? date('Y-m-d');
$worker_id = $_GET['worker_id'] ?? null;

// Handle attendance actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'mark_attendance') {
            $attendance_date = $_POST['attendance_date'];
            $workers_data = $_POST['workers'] ?? [];
            
            if (!empty($workers_data)) {
                $response = makeApiRequest('/attendance/attendance.php?action=bulk', 'POST', [
                    'attendance_date' => $attendance_date,
                    'workers' => $workers_data
                ]);

                if ($response && $response['success'] && isset($response['data']['success']) && $response['data']['success']) {
                    showAlert('Attendance marked successfully', 'success');
                } else {
                    $error_message = 'Failed to mark attendance';
                    if (isset($response['data']['message'])) {
                        $error_message = $response['data']['message'];
                    }
                    showAlert($error_message, 'danger');
                }
            }
        }
        
    } catch (Exception $e) {
        showAlert($e->getMessage(), 'danger');
    }
    
    header('Location: ' . $_SERVER['PHP_SELF'] . '?date=' . $selected_date);
    exit();
}

// Get attendance data for selected date
try {
    $api_url = "/attendance/attendance.php?action=date&date={$selected_date}";
    $attendance_response = makeApiRequest($api_url);



    // Handle the response format from makeApiRequest
    if ($attendance_response['success'] && isset($attendance_response['data'])) {
        $api_data = $attendance_response['data'];
        $workers = $api_data['workers'] ?? [];
        $summary = $api_data['summary'] ?? [];
    } else {
        // Fallback: empty data
        $workers = [];
        $summary = [];
    }


    
    // Get worker details if specific worker is selected
    $selected_worker = null;
    if ($worker_id) {
        $worker_response = makeApiRequest("/attendance/workers.php?worker_id={$worker_id}");
        $selected_worker = ($worker_response['success'] && isset($worker_response['data']['data']))
                          ? $worker_response['data']['data']
                          : null;

        // Get worker attendance for current month
        $start_date = date('Y-m-01', strtotime($selected_date));
        $end_date = date('Y-m-t', strtotime($selected_date));
        $worker_attendance_response = makeApiRequest("/attendance/attendance.php?action=worker&worker_id={$worker_id}&start_date={$start_date}&end_date={$end_date}");
        $worker_attendance_data = ($worker_attendance_response['success'] && isset($worker_attendance_response['data']['data']))
                                 ? $worker_attendance_response['data']['data']
                                 : [];
    }
    
} catch (Exception $e) {
    error_log("Attendance page error: " . $e->getMessage());
    $workers = [];
    $summary = [];
    $selected_worker = null;
    $worker_attendance_data = [];
}

include 'includes/header.php';
?>

<!-- Attendance Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Attendance Management</h1>
        <p class="text-muted">Track and manage worker attendance</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <button class="btn btn-primary" onclick="markBulkAttendance()">
                <i class="bi bi-check-all me-2"></i>
                Mark Bulk Attendance
            </button>
            <button class="btn btn-outline-primary" onclick="exportAttendance()">
                <i class="bi bi-download me-2"></i>
                Export
            </button>
        </div>
    </div>
</div>

<!-- Date Selection and Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar3 me-2"></i>
                        Select Date to View/Update Attendance
                    </h5>
                    <div class="d-flex align-items-center">
                        <div class="input-group" style="max-width: 250px;">
                            <span class="input-group-text"><i class="bi bi-calendar3"></i></span>
                            <input type="date" class="form-control" id="attendance_date" 
                                   value="<?= $selected_date ?>" onchange="changeDate(this.value)">
                        </div>
                        <button class="btn btn-outline-secondary ms-2" onclick="changeDate('<?= date('Y-m-d') ?>')">
                            <i class="bi bi-calendar-check"></i> Today
                        </button>
                    </div>
                </div>
                    <div class="col-md-8">
                        <div class="row text-center">
                            <div class="col">
                                <div class="border-end">
                                    <h4 class="text-success mb-0"><?= $summary['present'] ?? 0 ?></h4>
                                    <small class="text-muted">Present</small>
                                </div>
                            </div>
                            <div class="col">
                                <div class="border-end">
                                    <h4 class="text-danger mb-0"><?= $summary['absent'] ?? 0 ?></h4>
                                    <small class="text-muted">Absent</small>
                                </div>
                            </div>
                            <div class="col">
                                <div class="border-end">
                                    <h4 class="text-warning mb-0"><?= $summary['late'] ?? 0 ?></h4>
                                    <small class="text-muted">Late</small>
                                </div>
                            </div>
                            <div class="col">
                                <div class="border-end">
                                    <h4 class="text-info mb-0"><?= $summary['half_day'] ?? 0 ?></h4>
                                    <small class="text-muted">Half Day</small>
                                </div>
                            </div>
                            <div class="col">
                                <h4 class="text-secondary mb-0"><?= $summary['not_marked'] ?? 0 ?></h4>
                                <small class="text-muted">Not Marked</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Attendance Rate</h5>
                <?php 
                $total = ($summary['present'] ?? 0) + ($summary['absent'] ?? 0) + ($summary['late'] ?? 0) + ($summary['half_day'] ?? 0);
                $present_count = ($summary['present'] ?? 0) + ($summary['late'] ?? 0) + ($summary['half_day'] ?? 0);
                $attendance_rate = $total > 0 ? round(($present_count / $total) * 100, 1) : 0;
                ?>
                <h2 class="text-primary"><?= $attendance_rate ?>%</h2>
                <small class="text-muted">of <?= $summary['total_workers'] ?? 0 ?> workers</small>
            </div>
        </div>
    </div>
</div>

<?php if ($selected_worker): ?>
<!-- Worker Details Section -->
<div class="card mb-4">
    <div class="card shadow-sm">
    <div class="card-header bg-white py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-calendar-alt text-primary me-2"></i>
                Attendance for <span class="text-primary"><?php echo date('F j, Y', strtotime($selected_date)); ?></span>
            </h5>
            <div class="d-flex">
                <div class="input-group me-3" style="max-width: 250px;">
                    <span class="input-group-text bg-white"><i class="far fa-calendar-alt text-muted"></i></span>
                    <input type="date" class="form-control" id="datePicker" value="<?php echo $selected_date; ?>">
                </div>
                <div class="btn-group" role="group">
                    <a href="attendance.php?date=<?php echo date('Y-m-d', strtotime($selected_date . ' - 1 day')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <a href="attendance.php?date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary">
                        Today
                    </a>
                    <a href="attendance.php?date=<?php echo date('Y-m-d', strtotime($selected_date . ' + 1 day')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <div class="avatar-circle-lg mb-2">
                        <?= strtoupper(substr($selected_worker['name'], 0, 1)) ?>
                    </div>
                    <h6><?= htmlspecialchars($selected_worker['name']) ?></h6>
                    <small class="text-muted"><?= htmlspecialchars($selected_worker['employee_id']) ?></small>
                </div>
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Department:</strong> <?= htmlspecialchars($selected_worker['department'] ?? 'N/A') ?></p>
                        <p><strong>Position:</strong> <?= htmlspecialchars($selected_worker['position'] ?? 'N/A') ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Email:</strong> <?= htmlspecialchars($selected_worker['email'] ?? 'N/A') ?></p>
                        <p><strong>Phone:</strong> <?= htmlspecialchars($selected_worker['phone'] ?? 'N/A') ?></p>
                    </div>
                </div>
                
                <?php if (!empty($worker_attendance_data['statistics'])): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>Monthly Statistics</h6>
                        <div class="row">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-success"><?= $worker_attendance_data['statistics']['present_days'] ?></h5>
                                    <small>Present</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-danger"><?= $worker_attendance_data['statistics']['absent_days'] ?></h5>
                                    <small>Absent</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-warning"><?= $worker_attendance_data['statistics']['late_days'] ?></h5>
                                    <small>Late</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-info"><?= $worker_attendance_data['statistics']['half_days'] ?></h5>
                                    <small>Half Days</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-primary"><?= $worker_attendance_data['statistics']['attendance_percentage'] ?>%</h5>
                                    <small>Attendance</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-secondary"><?= $worker_attendance_data['statistics']['total_days'] ?></h5>
                                    <small>Total Days</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Attendance Table -->
<div class="card" id="attendanceTableSection">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-people-fill me-2"></i>
            Worker Attendance for <?= formatDate($selected_date, 'F j, Y') ?>
            <span class="badge bg-primary ms-2"><?= count($workers) ?> Workers</span>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($workers)): ?>
            <div class="text-center py-4">
                <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">No workers found for this date</p>
            </div>
        <?php else: ?>
            <form id="attendanceForm" method="POST">
                <input type="hidden" name="action" value="mark_attendance">
                <input type="hidden" name="attendance_date" value="<?= $selected_date ?>">
                
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th class="ps-4">Employee ID</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Position</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Check In</th>
                                <th class="text-center">Check Out</th>
                                <th class="text-end pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($workers) > 0): ?>
                                <?php foreach ($workers as $index => $worker): ?>
                                    <tr class="border-top">
                                        <input type="hidden" name="workers[<?= $index ?>][worker_id]" value="<?= $worker['id'] ?>">
                                        <td class="ps-4 fw-medium">
                                            <?= htmlspecialchars($worker['employee_id']) ?>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2 bg-primary text-white d-flex align-items-center justify-content-center"
                                                     style="width: 36px; height: 36px; font-size: 1rem;">
                                                    <?= strtoupper(substr($worker['name'], 0, 1)) ?>
                                                </div>
                                                <div>
                                                    <div class="fw-medium"><?= htmlspecialchars($worker['name']) ?></div>
                                                    <small class="text-muted">ID: <?= $worker['id'] ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-building me-1 text-primary"></i>
                                                <?= htmlspecialchars($worker['department'] ?? 'N/A') ?>
                                            </span>
                                        </td>
                                        <td><?= htmlspecialchars($worker['position'] ?? 'N/A') ?></td>
                                        <td class="text-center">
                                            <select class="form-select form-select-sm" name="workers[<?= $index ?>][status]"
                                                    onchange="toggleTimeFields(this, <?= $index ?>)" style="min-width: 120px;">
                                                <option value="not_marked" <?= ($worker['status'] ?? 'not_marked') === 'not_marked' ? 'selected' : '' ?>>Not Marked</option>
                                                <option value="present" <?= ($worker['status'] ?? '') === 'present' ? 'selected' : '' ?>>Present</option>
                                                <option value="absent" <?= ($worker['status'] ?? '') === 'absent' ? 'selected' : '' ?>>Absent</option>
                                                <option value="late" <?= ($worker['status'] ?? '') === 'late' ? 'selected' : '' ?>>Late</option>
                                                <option value="half_day" <?= ($worker['status'] ?? '') === 'half_day' ? 'selected' : '' ?>>Half Day</option>
                                                <option value="holiday" <?= ($worker['status'] ?? '') === 'holiday' ? 'selected' : '' ?>>Holiday</option>
                                            </select>
                                        </td>
                                        <td class="text-center">
                                            <input type="time" class="form-control form-control-sm time-fields-<?= $index ?>"
                                                   name="workers[<?= $index ?>][check_in_time]" id="check_in_<?= $index ?>"
                                                   value="<?= $worker['check_in_time'] ?? '' ?>" style="width: 120px;">
                                        </td>
                                        <td class="text-center">
                                            <input type="time" class="form-control form-control-sm time-fields-<?= $index ?>"
                                                   name="workers[<?= $index ?>][check_out_time]" id="check_out_<?= $index ?>"
                                                   value="<?= $worker['check_out_time'] ?? '' ?>" style="width: 120px;">
                                        </td>
                                        <td class="text-end pe-4">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill px-3 ms-1"
                                                        data-bs-toggle="modal" data-bs-target="#attendanceModal"
                                                        data-worker-id="<?= $worker['id'] ?>"
                                                        data-worker-name="<?= htmlspecialchars($worker['name']) ?>"
                                                        data-status="<?= $worker['status'] ?? 'not_marked' ?>"
                                                        data-notes="<?= htmlspecialchars($worker['notes'] ?? '') ?>">
                                                    <i class="fas fa-ellipsis-h"></i> More
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-users-slash fa-2x mb-2"></i>
                                            <p class="mb-0">No workers found for this date.</p>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <button type="submit" name="save_attendance" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Save Attendance
                    </button>
                    <a href="attendance.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to List
                    </a>
                </div>
                
            </form>
        <?php endif; ?>
        </div>
    </div>
</div>
        
        <!-- Attendance Modal -->
        <div class="modal fade" id="attendanceModal" tabindex="-1" aria-labelledby="attendanceModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form id="attendanceForm" method="post" action="">
                        <div class="modal-header">
                            <h5 class="modal-title" id="attendanceModalLabel">Update Attendance</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <input type="hidden" name="worker_id" id="modalWorkerId">
                            <input type="hidden" name="date" value="<?= $selected_date ?>">
                            
                            <div class="mb-3">
                                <label class="form-label">Employee</label>
                                <input type="text" class="form-control" id="modalWorkerName" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-select" name="status" id="statusSelect" required>
                                    <option value="present">Present</option>
                                    <option value="absent">Absent</option>
                                    <option value="late">Late</option>
                                    <option value="half_day">Half Day</option>
                                    <option value="not_marked">Not Marked</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Check In</label>
                                <input type="time" class="form-control" name="check_in_time" id="checkInTime">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Check Out</label>
                                <input type="time" class="form-control" name="check_out_time" id="checkOutTime">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Break Duration (minutes)</label>
                                <input type="number" class="form-control" name="break_duration" id="breakDuration" min="0" max="480" value="0">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Overtime (hours)</label>
                                <input type="number" class="form-control" name="overtime_hours" id="overtimeHours" min="0" max="24" step="0.5" value="0">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea class="form-control" name="notes" id="notesTextarea" rows="3" placeholder="Add any notes here..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary" name="update_attendance">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

<script>
// Initialize the attendance modal with worker data
document.addEventListener('DOMContentLoaded', function() {
    const attendanceModal = document.getElementById('attendanceModal');
    
    // Initialize modal with worker data when shown
    if (attendanceModal) {
        attendanceModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const workerId = button.getAttribute('data-worker-id');
            const workerName = button.getAttribute('data-worker-name');
            const status = button.getAttribute('data-status');
            const notes = button.getAttribute('data-notes') || '';
            
            // Update modal content
            document.getElementById('modalWorkerId').value = workerId;
            document.getElementById('modalWorkerName').value = workerName;
            document.getElementById('statusSelect').value = status;
            document.getElementById('notesTextarea').value = notes;
            
            // Update form action
            const form = document.getElementById('attendanceForm');
            form.action = `attendance.php?date=<?= $selected_date ?>&worker_id=${workerId}`;
        });
    }
    
    // Handle date picker change
    const datePicker = document.getElementById('datePicker');
    if (datePicker) {
        datePicker.addEventListener('change', function() {
            window.location.href = `attendance.php?date=${this.value}`;
        });
    }
    
    // Handle form submission with fetch API for better UX
    const attendanceForm = document.getElementById('attendanceForm');
    if (attendanceForm) {
        attendanceForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            
            // Show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
            
            // Submit form data
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showAlert('Attendance updated successfully!', 'success');
                    // Close modal and reload page after a short delay
                    const modal = bootstrap.Modal.getInstance(attendanceModal);
                    if (modal) modal.hide();
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    throw new Error(data.message || 'Failed to update attendance');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert(error.message || 'An error occurred while updating attendance.', 'danger');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            });
        });
    }
});

// Function to show alert messages
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // Create container if it doesn't exist
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        document.querySelector('.container-fluid').prepend(alertContainer);
    }
    
    // Add alert to container
    alertContainer.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-remove alert after 5 seconds
    setTimeout(() => {
        const alert = alertContainer.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

// Set attendance status for a worker
function setAttendanceStatus(button, workerId, status) {
    // Remove active class from all buttons in the same group
    const buttonGroup = button.closest('.btn-group');
    buttonGroup.querySelectorAll('.btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Add active class to clicked button
    button.classList.add('active');
    
    // Update the hidden input value
    const statusInput = buttonGroup.querySelector('input[type="hidden"]');
    if (statusInput) {
        statusInput.value = status;
    }
}

// Toggle time fields based on status
function toggleTimeFields(select, index) {
    const timeFields = document.querySelectorAll(`.time-fields-${index}`);
    const showTimeFields = ['present', 'late', 'half_day'].includes(select.value);
    
    timeFields.forEach(field => {
        field.style.display = showTimeFields ? 'block' : 'none';
        if (!showTimeFields) {
            field.value = '';
        }
    });
}

// Initialize attendance management
document.addEventListener('DOMContentLoaded', function() {
    // Highlight today's date in the date picker
    const today = new Date().toISOString().split('T')[0];
    const selectedDate = '<?= $selected_date ?>';
    
    // Show attendance table if date is selected
    if (selectedDate) {
        document.getElementById('attendanceTableSection').style.display = 'block';
    }
    
    // Set focus on date input
    document.getElementById('attendance_date').focus();
    
    // Initialize time fields based on current status
    document.querySelectorAll('select[name*="[status]"]').forEach(function(select, index) {
        toggleTimeFields(select, index);
    });
});

// Change date function
function changeDate(date) {
    const url = new URL(window.location);
    url.searchParams.set('date', date);
    window.location.href = url.toString();
}

// Toggle time fields based on status
function toggleTimeFields(statusSelect, index) {
    const status = statusSelect.value;
    const checkInField = document.getElementById(`check_in_${index}`);
    const checkOutField = document.getElementById(`check_out_${index}`);

    if (status === 'absent' || status === 'holiday') {
        checkInField.disabled = true;
        checkOutField.disabled = true;
        checkInField.value = '';
        checkOutField.value = '';
    } else {
        checkInField.disabled = false;
        checkOutField.disabled = false;

        // Set default times for present status
        if (status === 'present' && !checkInField.value) {
            checkInField.value = '09:00';
            checkOutField.value = '17:00';
        } else if (status === 'late' && !checkInField.value) {
            checkInField.value = '09:30';
            checkOutField.value = '17:00';
        } else if (status === 'half_day' && !checkInField.value) {
            checkInField.value = '09:00';
            checkOutField.value = '13:00';
        }
    }
}

// Format date for display
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

// Mark bulk attendance
function markBulkAttendance() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'bulkAttendanceModal';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mark Bulk Attendance</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="bulkAttendanceForm">
                        <div class="mb-3">
                            <label class="form-label">Select Status for All Workers</label>
                            <select class="form-select" id="bulkStatus" required>
                                <option value="">Choose status...</option>
                                <option value="present">Present</option>
                                <option value="absent">Absent</option>
                                <option value="late">Late</option>
                                <option value="half_day">Half Day</option>
                                <option value="holiday">Holiday</option>
                            </select>
                        </div>
                        <div class="mb-3" id="bulkTimeFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Check In Time</label>
                                    <input type="time" class="form-control" id="bulkCheckIn" value="09:00">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Check Out Time</label>
                                    <input type="time" class="form-control" id="bulkCheckOut" value="17:00">
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            This will apply the selected status to all workers for the current date.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="applyBulkBtn">Apply to All</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to the DOM
    document.body.appendChild(modal);
    
    // Initialize Bootstrap modal
    const bsModal = new bootstrap.Modal(modal);
    
    // Store the modal instance in the modal element for later use
    modal._bsModal = bsModal;
    
    // Show the modal
    bsModal.show();

    // Handle status change for bulk
    modal.querySelector('#bulkStatus').addEventListener('change', function() {
        const timeFields = modal.querySelector('#bulkTimeFields');
        const checkIn = modal.querySelector('#bulkCheckIn');
        const checkOut = modal.querySelector('#bulkCheckOut');

        if (this.value === 'absent' || this.value === 'holiday') {
            timeFields.style.display = 'none';
        } else {
            timeFields.style.display = 'block';

            if (this.value === 'present') {
                checkIn.value = '09:00';
                checkOut.value = '17:00';
            } else if (this.value === 'late') {
                checkIn.value = '09:30';
                checkOut.value = '17:00';
            } else if (this.value === 'half_day') {
                checkIn.value = '09:00';
                checkOut.value = '13:00';
            }
        }
    });
    
    // Handle apply button click
    modal.querySelector('#applyBulkBtn').addEventListener('click', function() {
        applyBulkAttendance(bsModal);
    });

    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        // Remove modal from DOM after it's hidden
        if (document.body.contains(modal)) {
            document.body.removeChild(modal);
        }
    });
}

// Apply bulk attendance
function applyBulkAttendance(modalInstance) {
    const modal = document.getElementById('bulkAttendanceModal');
    const status = modal.querySelector('#bulkStatus').value;
    const checkIn = modal.querySelector('#bulkCheckIn').value;
    const checkOut = modal.querySelector('#bulkCheckOut').value;

    if (!status) {
        showToast('Please select a status', 'error');
        return;
    }

    // Apply to all status selects
    document.querySelectorAll('select[name*="[status]"]').forEach(function(select, index) {
        select.value = status;
        toggleTimeFields(select, index);

        if (status !== 'absent' && status !== 'holiday') {
            const checkInField = document.getElementById(`check_in_${index}`);
            const checkOutField = document.getElementById(`check_out_${index}`);
            if (checkInField) checkInField.value = checkIn;
            if (checkOutField) checkOutField.value = checkOut;
        }
    });

    // Close the modal using the provided instance or find it
    if (modalInstance && typeof modalInstance.hide === 'function') {
        modalInstance.hide();
    } else {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }
    
    showToast('Bulk attendance applied successfully', 'success');
}

// Export attendance
function exportAttendance() {
    const date = document.getElementById('attendance_date').value;
    window.open(`/api/attendance/export.php?date=${date}&format=csv`, '_blank');
}

// Quick status buttons
function setQuickStatus(status) {
    document.querySelectorAll('select[name*="[status]"]').forEach(function(select, index) {
        if (!select.value) { // Only set if not already set
            select.value = status;
            toggleTimeFields(select, index);
        }
    });
}

// Add quick action buttons
document.addEventListener('DOMContentLoaded', function() {
    const cardHeader = document.querySelector('.card:last-child .card-header');
    if (cardHeader) {
        const quickActions = document.createElement('div');
        quickActions.className = 'btn-group btn-group-sm ms-auto';
        quickActions.innerHTML = `
            <button type="button" class="btn btn-outline-success" onclick="setQuickStatus('present')" title="Mark all empty as Present">
                <i class="bi bi-check-circle me-1"></i>Present
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="setQuickStatus('absent')" title="Mark all empty as Absent">
                <i class="bi bi-x-circle me-1"></i>Absent
            </button>
        `;

        cardHeader.style.display = 'flex';
        cardHeader.style.justifyContent = 'space-between';
        cardHeader.style.alignItems = 'center';
        cardHeader.appendChild(quickActions);
    }
});

// Toggle time fields based on status
function toggleTimeFields(select, index) {
    const status = select.value;
    const timeFields = document.querySelectorAll(`#check_in_${index}, #check_out_${index}`);
    
    // Show/hide time fields based on status
    if (status === 'present' || status === 'late' || status === 'half_day') {
        timeFields.forEach(field => field.style.display = 'block');
    } else {
        timeFields.forEach(field => field.style.display = 'none');
    }
}
</script>

<?php include 'includes/footer.php'; ?>
